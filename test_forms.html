<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Test</title>
</head>
<body>
    <h1>Testing Form Functionality</h1>
    
    <script>
        // Test if the main application loads correctly
        function testFormFunctionality() {
            console.log('Testing form functionality...');
            
            // Test 1: Check if forms exist
            const returnForm = document.getElementById('returnForm');
            const exchangeForm = document.getElementById('exchangeForm');
            
            console.log('Return form exists:', !!returnForm);
            console.log('Exchange form exists:', !!exchangeForm);
            
            // Test 2: Check if product selection works
            const returnProducts = document.querySelectorAll('#returnProductList .product-item');
            const exchangeProducts = document.querySelectorAll('#exchangeProductList .product-item');
            
            console.log('Return products found:', returnProducts.length);
            console.log('Exchange products found:', exchangeProducts.length);
            
            // Test 3: Check if validation functions exist
            console.log('validateForm function exists:', typeof validateForm === 'function');
            console.log('selectProduct function exists:', typeof selectProduct === 'function');
            console.log('addStatusItem function exists:', typeof addStatusItem === 'function');
            console.log('showConfirmationPopup function exists:', typeof showConfirmationPopup === 'function');
            
            return {
                returnForm: !!returnForm,
                exchangeForm: !!exchangeForm,
                returnProducts: returnProducts.length,
                exchangeProducts: exchangeProducts.length,
                functions: {
                    validateForm: typeof validateForm === 'function',
                    selectProduct: typeof selectProduct === 'function',
                    addStatusItem: typeof addStatusItem === 'function',
                    showConfirmationPopup: typeof showConfirmationPopup === 'function'
                }
            };
        }
        
        // Load the main application in an iframe and test
        window.addEventListener('load', () => {
            const iframe = document.createElement('iframe');
            iframe.src = 'returns.html';
            iframe.style.width = '100%';
            iframe.style.height = '600px';
            iframe.style.border = '1px solid #ccc';
            
            iframe.onload = () => {
                console.log('Main application loaded');
                
                // Access iframe content
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const iframeWindow = iframe.contentWindow;
                    
                    // Test form elements
                    const returnForm = iframeDoc.getElementById('returnForm');
                    const exchangeForm = iframeDoc.getElementById('exchangeForm');
                    
                    console.log('Return form in iframe:', !!returnForm);
                    console.log('Exchange form in iframe:', !!exchangeForm);
                    
                    // Test if functions are available
                    console.log('Functions available in iframe:');
                    console.log('- validateForm:', typeof iframeWindow.validateForm);
                    console.log('- selectProduct:', typeof iframeWindow.selectProduct);
                    console.log('- addStatusItem:', typeof iframeWindow.addStatusItem);
                    console.log('- showConfirmationPopup:', typeof iframeWindow.showConfirmationPopup);
                    console.log('- toggleAccordion:', typeof iframeWindow.toggleAccordion);
                    
                } catch (e) {
                    console.error('Cannot access iframe content due to security restrictions:', e.message);
                    console.log('This is normal for file:// URLs. The application should work when accessed directly.');
                }
            };
            
            document.body.appendChild(iframe);
        });
    </script>
</body>
</html>
