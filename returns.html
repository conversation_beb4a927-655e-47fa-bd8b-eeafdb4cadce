<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Returns & Exchanges - Portal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2EC0CB ;
            --text-color-light: #333A3D;
            --secondary-color: #ff5722;
            --background-color-light: #f8f9fa;
            --border-color-light: #dee2e6;
            --card-bg-light: #ffffff;
            --success-color: #28a745;
            --sidebar-width: 240px;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background-color-light);
            color: var(--text-color-light);
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
        }



        .top-section {
            display: flex;
            padding: 20px;
            gap: 30px;
            border-bottom: 1px solid var(--border-color-light);
            flex-shrink: 0;
        }

        .dashboard-area {
            flex: 0 0 65%;
        }

        .navigation-area {
            flex: 0 0 35%;
            display: flex;
            align-items: flex-end;
        }

        .bottom-section {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .content-area {
            width: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-section-container {
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            overflow-y: auto;
            display: none;
        }

        .content-section-container.active {
            display: block;
        }

        .status-section {
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            overflow-y: auto;
            display: none;
        }

        .status-section.active {
            display: block;
        }

        .content-section {
            margin-bottom: 50px;
            scroll-margin-top: 20px;
        }

        h1 { font-size: 1.8rem; font-weight: 700; margin-bottom: 5px; color: var(--primary-color); }
        .section-description { font-size: 0.95rem; color: #6c757d; margin-bottom: 1rem; }

        /* Dashboard */
        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background-color: var(--card-bg-light);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .stat-card .icon {
            font-size: 2rem;
            color: var(--primary-color);
        }
        .stat-card .info .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
        }
         .stat-card .info .stat-title {
            font-size: 0.9rem;
            color: #6c757d;
        }


        .status-badge { padding: 5px 12px; border-radius: 20px; font-weight: 600; font-size: 0.8rem; }
        .status-badge.delivered { background-color: #d4edda; color: #155724; }
        .status-badge.pending { background-color: #fff3cd; color: #856404; }
        .status-badge.completed { background-color: #d1ecf1; color: #0c5460; }
        .hidden { display: none !important; }

        /* Horizontal Navigation Bar */
        .horizontal-nav {
            width: 100%;
            background-color: var(--card-bg-light);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 0;
            overflow: hidden;
            height: fit-content;
        }

        .horizontal-nav-list {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .horizontal-nav-item {
            flex: 1;
            border-right: 1px solid var(--border-color-light);
        }

        .horizontal-nav-item:last-child {
            border-right: none;
        }

        .horizontal-nav-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px 10px;
            text-decoration: none;
            color: var(--text-color-light);
            font-weight: 600;
            font-size: 0.85rem;
            transition: background-color 0.2s, color 0.2s;
            cursor: pointer;
            min-height: 80px;
        }

        .horizontal-nav-link:hover {
            background-color: var(--background-color-light);
        }

        .horizontal-nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .horizontal-nav-link i {
            font-size: 1.3rem;
            margin-bottom: 6px;
        }

        /* Forms */
        .form-card {
            background-color: var(--card-bg-light);
            padding: 30px;
            border-radius: 8px;
            border: 1px solid var(--border-color-light);
        }
        .tab-buttons { display: flex; border-bottom: 1px solid var(--border-color-light); margin-bottom: 2rem; }
        .tab-btn { background: none; border: none; padding: 15px 20px; font-size: 1.1rem; font-weight: 600; cursor: pointer; border-bottom: 3px solid transparent; }
        .tab-btn.active { border-bottom-color: var(--primary-color); color: var(--primary-color); }
        .tab-pane { display: none; }
        .tab-pane.active { display: block; }
        .form-group { margin-bottom: 1.5rem; }
        .form-group label { display: block; font-weight: 600; margin-bottom: 8px; }
        select, textarea {
            width: 100%; padding: 12px; border: 1px solid var(--border-color-light); border-radius: 5px; box-sizing: border-box;
        }
        .submit-btn {
            background-color: var(--primary-color); color: white; border: none; padding: 15px 25px; border-radius: 5px; font-size: 1rem; font-weight: 600; cursor: pointer; width: 100%; margin-top: 10px;
        }
        .item-details-content { border: 1px solid var(--border-color-light); border-radius: 8px; padding: 15px; min-height: 50px; }
        .radio-group-label { font-weight: 600; margin-bottom: 8px; display:block; }
        .radio-group { display: flex; flex-direction: column; gap: 10px; }
        .radio-group label { display: flex; align-items: center; gap: 10px; font-weight: 500; }

        /* Exchange Product Selection */
        .exchange-product-selection {
            background-color: var(--background-color-light);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 20px;
            margin-top: 10px;
        }
        .exchange-product-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .exchange-product-item:hover {
            background-color: var(--border-color-light);
        }
        .price-difference-section {
            padding: 15px;
            border: 1px solid var(--primary-color);
            background-color: #f0f7ff;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
        }

        /* Drag and Drop File Upload */
        .file-upload-label {
            display: flex; flex-direction: column; align-items: center; justify-content: center;
            padding: 30px; border: 2px dashed var(--border-color-light); border-radius: 8px;
            cursor: pointer; text-align: center; transition: background-color .2s, border-color .2s;
        }
        .file-upload-label.highlight {
            border-color: var(--primary-color);
            background-color: #f0f7ff;
        }
        .file-upload-label i { font-size: 2rem; color: var(--primary-color); margin-bottom: 10px;}
        .file-list { margin-top: 10px; font-size: 0.9rem; }
        .file-list ul { list-style-type: none; padding: 0;}
        .file-list li { background-color: #f1f1f1; padding: 5px 10px; border-radius: 4px; margin-top: 5px; }


        /* Success Popup */
        .success-popup {
            display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.6); z-index: 1000;
            align-items: center; justify-content: center;
        }
        .confirmation-content { background-color: var(--card-bg-light); padding: 40px; border-radius: 10px; text-align: center; max-width: 500px; }
        .confirmation-icon i { font-size: 60px; color: var(--success-color); margin-bottom: 20px; }
        .confirmation-details { margin: 30px 0; text-align: left; }
        .confirmation-detail { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid var(--border-color-light); }
        .confirmation-btn { padding: 12px 20px; border-radius: 5px; border: none; cursor: pointer; font-weight: 600; }
        .confirmation-btn.secondary { background-color: var(--border-color-light); }

        /* STATUS PAGE - NEW STYLES WITH ACCORDION */
        .status-items-container { display: flex; flex-direction: column; gap: 1rem; }
        .status-item-card {
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            background-color: var(--card-bg-light);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .status-accordion-header {
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--card-bg-light);
            border-bottom: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .status-accordion-header:hover {
            background-color: var(--background-color-light);
        }

        .status-accordion-header.expanded {
            border-bottom-color: var(--border-color-light);
        }

        .status-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .status-header-left {
            display: flex;
            flex-direction: column;
        }

        .status-header-left h3 {
            margin: 0;
            font-size: 1.2rem;
            color: var(--primary-color);
        }

        .status-header-left p {
            margin: 5px 0 0;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .status-header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .accordion-toggle {
            font-size: 1.2rem;
            color: var(--primary-color);
            transition: transform 0.3s ease;
        }

        .accordion-toggle.expanded {
            transform: rotate(180deg);
        }

        .status-accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .status-accordion-content.expanded {
            max-height: 1000px;
        }

        .status-accordion-body {
            padding: 0 20px 20px 20px;
        }
        .status-card-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;}
        .status-card-header h3 { margin: 0; font-size: 1.3rem; }
        .status-card-header p { margin: 5px 0 0; color: #6c757d; }
        .status-card-product { display: flex; align-items: center; gap: 20px; border-bottom: 1px solid var(--border-color-light); padding-bottom: 20px; margin-bottom: 20px; }
        .status-product-details { display: flex; flex-wrap: wrap; gap: 10px 30px; flex-grow: 1; }
        .status-product-details > div { display: flex; flex-direction: column; }
        .status-product-details span:first-child { font-weight: 600; }
        .status-product-details span:last-child { color: #6c757d; }

        .progress-tracker { display: flex; justify-content: space-between; position: relative; margin: 30px 0; }
        .progress-tracker::before {
            content: '';
            position: absolute;
            top: 15px; /* (icon height / 2) */
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--border-color-light);
            z-index: 1;
        }
        .progress-step { display: flex; flex-direction: column; align-items: center; text-align: center; width: 120px; position: relative; z-index: 2; }
        .progress-icon {
            width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center;
            background-color: var(--card-bg-light); border: 2px solid var(--border-color-light);
            margin-bottom: 10px;
        }
        .progress-icon i { font-size: 14px; }
        .progress-step-title { font-weight: 600; font-size: 0.9rem; }
        .progress-step-date { font-size: 0.8rem; color: #6c757d; }

        .progress-step.completed .progress-icon { border-color: var(--success-color); background-color: var(--success-color); color: white; }
        .progress-step.active .progress-icon { border-color: var(--primary-color); background-color: var(--primary-color); color: white; }
        .progress-step.active .progress-step-title { color: var(--primary-color); }

        .status-actions { display: flex; gap: 15px; margin-top: 20px; }
        .status-actions .action-btn { background-color: transparent; border: 1px solid var(--border-color-light); padding: 10px 15px; border-radius: 5px; font-weight: 600; cursor: pointer; }
        .status-actions .action-btn.primary { background-color: var(--primary-color); color: white; border-color: var(--primary-color); }

        /* Sorting and Filtering Controls */
        .status-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 15px;
            background-color: var(--card-bg-light);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            gap: 20px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-label {
            font-weight: 600;
            color: var(--text-color-light);
            font-size: 0.9rem;
        }

        .control-select {
            padding: 8px 12px;
            border: 1px solid var(--border-color-light);
            border-radius: 5px;
            background-color: white;
            font-size: 0.9rem;
            cursor: pointer;
            min-width: 150px;
        }

        .filter-buttons {
            display: flex;
            gap: 8px;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 1px solid var(--border-color-light);
            background-color: white;
            border-radius: 5px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-btn:hover {
            background-color: var(--background-color-light);
        }

        .filter-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .status-items-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .status-item-card.hidden {
            display: none;
        }
    </style>

</head>
<body>
    <div class="app-container">
        <!-- Top Section: Dashboard + Navigation -->
        <div class="top-section">
            <div class="dashboard-area">
                <section id="dashboard-section" class="content-section">
                    <h1>Dashboard</h1>
                    <div class="dashboard-container">
                        <div class="stat-card">
                            <div class="icon"><i class="fas fa-shopping-cart"></i></div>
                            <div class="info">
                                <div id="orders-count" class="stat-value">15</div>
                                <div class="stat-title">Total Orders</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="icon"><i class="fas fa-undo-alt"></i></div>
                            <div class="info">
                                <div id="returned-count" class="stat-value">3</div>
                                <div class="stat-title">Items Returned</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="icon"><i class="fas fa-exchange-alt"></i></div>
                            <div class="info">
                                <div id="exchanged-count" class="stat-value">2</div>
                                <div class="stat-title">Items Exchanged</div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <div class="navigation-area">
                <!-- Horizontal Navigation Bar -->
                <nav class="horizontal-nav">
                    <ul class="horizontal-nav-list">
                        <li class="horizontal-nav-item">
                            <a href="#" class="horizontal-nav-link active" data-section="status">
                                <i class="fas fa-tasks"></i>
                                <span>Status</span>
                            </a>
                        </li>
                        <li class="horizontal-nav-item">
                            <a href="#" class="horizontal-nav-link" data-section="request">
                                <i class="fas fa-plus-circle"></i>
                                <span>New Request</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Bottom Section: Full-width content -->
        <div class="bottom-section">
            <div class="content-area">
                <!-- Status Section -->
                <div id="status-content" class="status-section active">
                    <section id="status-section" class="content-section">
                        <h1>Request Status</h1>
                        <p class="section-description">Track the progress of your submitted return and exchange requests.</p>

                        <!-- Sorting and Filtering Controls -->
                        <div class="status-controls">
                            <div class="control-group">
                                <label class="control-label" for="sortSelect">Sort by:</label>
                                <select id="sortSelect" class="control-select">
                                    <option value="date-desc">Date: Latest to Oldest</option>
                                    <option value="date-asc">Date: Oldest to Latest</option>
                                    <option value="type-returns">Type: Returns First</option>
                                    <option value="type-exchanges">Type: Exchanges First</option>
                                </select>
                            </div>

                            <div class="control-group">
                                <span class="control-label">Filter by Status:</span>
                                <div class="filter-buttons">
                                    <button class="filter-btn active" data-filter="all">All</button>
                                    <button class="filter-btn" data-filter="pending">Pending</button>
                                    <button class="filter-btn" data-filter="active">In Progress</button>
                                    <button class="filter-btn" data-filter="completed">Completed</button>
                                </div>
                            </div>
                        </div>

                        <div id="status-items-container" class="status-items-container">
                            <!-- Status items will be added here by JavaScript -->
                        </div>
                    </section>
                </div>

                <!-- New Request Section -->
                <div id="request-content" class="content-section-container">
                    <section id="request-section" class="content-section">
                        <h1>New Return or Exchange Request</h1>
                        <p class="section-description">Create a new return or exchange request.</p>
                    <div class="form-card">
                        <div class="tab-buttons">
                            <button class="tab-btn active" data-tab="return">Return</button>
                            <button class="tab-btn" data-tab="exchange">Exchange</button>
                        </div>
                        <div class="tab-content">
                            <div id="return" class="tab-pane active">
                                <form id="returnForm">
                                    <div class="form-group">
                                        <label for="returnOrderNumber">Order Number*</label>
                                        <input type="text" id="returnOrderNumber" placeholder="Enter your order number (e.g., ORD12345)" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="returnProductName">Product Name*</label>
                                        <input type="text" id="returnProductName" placeholder="Enter the product name" required>
                                    </div>
                                <div class="form-group">
                                    <label for="returnReason">Reason for Return*</label>
                                    <select id="returnReason" required>
                                        <option value="">Select a reason...</option>
                                        <option value="wrong-part">Wrong Part Received</option>
                                        <option value="damaged">Part Damaged/Defective</option>
                                        <option value="not-as-described">Not as Described</option>
                                        <option value="no-longer-needed">No Longer Needed</option>
                                        <option value="better-price">Found Better Price Elsewhere</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="returnComments">Comments*</label>
                                    <textarea id="returnComments" rows="4" placeholder="Please provide details about your return request..." required></textarea>
                                </div>
                                 <div class="form-group">
                                    <label>Upload Image/Video Evidence*</label>
                                    <div class="file-upload-container">
                                        <label for="returnFiles" class="file-upload-label">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <span>Choose files or drag & drop</span>
                                        </label>
                                        <input type="file" id="returnFiles" class="hidden" multiple required>
                                        <div id="returnFilesList" class="file-list"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <span class="radio-group-label">Return Method*</span>
                                    <div class="radio-group">
                                        <label><input type="radio" name="returnMethod" value="courier" checked> Courier Pickup</label>
                                        <label><input type="radio" name="returnMethod" value="store"> Return to Store</label>
                                        <label><input type="radio" name="returnMethod" value="warehouse"> Warehouse Drop-off</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <span class="radio-group-label">Refund Method*</span>
                                    <div class="radio-group">
                                        <label><input type="radio" name="refundMethod" value="original" checked> Original Payment Method</label>
                                        <label><input type="radio" name="refundMethod" value="credit"> Store Credit</label>
                                    </div>
                                </div>
                                <button type="submit" class="submit-btn">Submit Return Request</button>
                            </form>
                        </div>
                            <div id="exchange" class="tab-pane">
                                <form id="exchangeForm">
                                    <div class="form-group">
                                        <label for="exchangeOrderNumber">Order Number*</label>
                                        <input type="text" id="exchangeOrderNumber" placeholder="Enter your order number (e.g., ORD12345)" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="exchangeProductName">Product Name*</label>
                                        <input type="text" id="exchangeProductName" placeholder="Enter the product name" required>
                                    </div>
                                <div class="form-group">
                                    <label for="exchangeReason">Reason for Exchange*</label>
                                    <select id="exchangeReason" required>
                                        <option value="">Select a reason...</option>
                                        <option value="wrong-part">Wrong Part Received</option>
                                        <option value="incorrect-fitment">Incorrect Fitment</option>
                                        <option value="damaged">Part Damaged/Defective</option>
                                        <option value="upgrade">Upgrade/Different Part</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                 <div class="form-group">
                                    <label>Upload Image/Video Evidence*</label>
                                     <div class="file-upload-container">
                                        <label for="exchangeFiles" class="file-upload-label">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <span>Choose files or drag & drop</span>
                                        </label>
                                        <input type="file" id="exchangeFiles" class="hidden" multiple required>
                                        <div id="exchangeFilesList" class="file-list"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="exchangeComments">Comments*</label>
                                    <textarea id="exchangeComments" rows="4" placeholder="Please provide details about your exchange request..." required></textarea>
                                </div>
                                <div class="form-group">
                                    <label>Choose New Product*</label>
                                    <div class="exchange-product-selection" id="exchangeProductSelection">
                                        <!-- Mock product list -->
                                        <div class="exchange-product-item" onclick="handleExchangeSelection(this)" data-name="Premium Spark Plug" data-price="19.99">
                                            <span>Premium Spark Plug</span><strong>$19.99</strong>
                                        </div>
                                         <div class="exchange-product-item" onclick="handleExchangeSelection(this)" data-name="Performance Oil Filter" data-price="15.49">
                                            <span>Performance Oil Filter</span><strong>$15.49</strong>
                                        </div>
                                        <div class="exchange-product-item" onclick="handleExchangeSelection(this)" data-name="Standard Wiper Blade" data-price="7.99">
                                            <span>Standard Wiper Blade</span><strong>$7.99</strong>
                                        </div>
                                    </div>
                                    <div class="item-details-content hidden" id="newProductDetails"></div>
                                    <input type="hidden" id="newProductName" name="newProductName" required>
                                </div>
                                            <div id="priceDifferenceSection" class="hidden"></div>
                                            <button type="submit" class="submit-btn">Submit Exchange Request</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>

    <div id="confirmationPopup" class="success-popup">
        <div class="confirmation-content">
            <div class="confirmation-icon"><i class="fas fa-check-circle"></i></div>
            <h3>Request Submitted!</h3>
            <div class="confirmation-details">
                <div class="confirmation-detail"><span class="detail-label">Request #:</span><span class="detail-value" id="requestNumber"></span></div>
                <div class="confirmation-detail"><span class="detail-label">Order #:</span><span class="detail-value" id="confirmationOrderNumber"></span></div>
                <div class="confirmation-detail"><span class="detail-label">Type:</span><span class="detail-value" id="requestType"></span></div>
            </div>
            <div class="confirmation-actions">
                <button id="viewStatusBtn" class="confirmation-btn">View Status</button>
                <button id="closePopupBtn" class="confirmation-btn secondary">Close</button>
            </div>
        </div>
    </div>


    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // --- INITIALIZATION ---
        initHorizontalNav();
        initRequestForms();
        initConfirmationPopup();
        loadSampleStatusData();
        initStatusControls();
    });

    // --- DASHBOARD ---
    function updateDashboard(type, count) {
        if(type === 'Return') {
            const returnedEl = document.getElementById('returned-count');
            returnedEl.textContent = parseInt(returnedEl.textContent) + count;
        } else if (type === 'Exchange') {
            const exchangedEl = document.getElementById('exchanged-count');
            exchangedEl.textContent = parseInt(exchangedEl.textContent) + count;
        }
        // Initial call
        if(!type) {
            document.getElementById('orders-count').textContent = document.querySelectorAll('.order-item').length;
        }
    }

    // --- HORIZONTAL NAVIGATION ---
    function initHorizontalNav() {
        const navLinks = document.querySelectorAll('.horizontal-nav-link');

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.currentTarget.dataset.section;

                // Update active state
                navLinks.forEach(l => l.classList.remove('active'));
                e.currentTarget.classList.add('active');

                // Hide all content sections
                hideAllContentSections();

                // Show the selected section
                showContentSection(section);
            });
        });

        // Set default active section on page load
        showContentSection('status');
    }

    function hideAllContentSections() {
        const sections = [
            document.getElementById('status-content'),
            document.getElementById('request-content')
        ];

        sections.forEach(section => {
            if (section) {
                section.classList.remove('active');
            }
        });
    }

    function showContentSection(sectionName) {
        let targetSection;

        switch(sectionName) {
            case 'status':
                targetSection = document.getElementById('status-content');
                break;
            case 'request':
                targetSection = document.getElementById('request-content');
                break;
            default:
                targetSection = document.getElementById('status-content');
        }

        if (targetSection) {
            targetSection.classList.add('active');
        }
    }

    // --- NAVIGATION HELPERS ---
    function switchTab(type) {
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        document.querySelector(`.tab-btn[data-tab="${type}"]`).classList.add('active');
        document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
        document.getElementById(type).classList.add('active');
    }

    window.navigateToRequest = (type) => {
        // Show the request section
        hideAllContentSections();
        showContentSection('request');

        // Update navigation active state
        const navLinks = document.querySelectorAll('.horizontal-nav-link');
        navLinks.forEach(l => l.classList.remove('active'));
        document.querySelector('.horizontal-nav-link[data-section="request"]').classList.add('active');

        // Switch to the appropriate tab
        switchTab(type);
    };



    // --- REQUEST FORMS ---
    function initRequestForms() {
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.target.dataset.tab;
                switchTab(tabId);
                localStorage.removeItem('selectedProducts');
            });
        });

        initDragAndDrop('returnFiles', 'returnFilesList');
        initDragAndDrop('exchangeFiles', 'exchangeFilesList');

        document.getElementById('returnForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            if(!validateForm('return')) return;

            const orderNumber = document.getElementById('returnOrderNumber').value;
            const requestNumber = 'REQ-' + Math.floor(100000 + Math.random() * 900000);
            addStatusItem(requestNumber, 'Return');
            showConfirmationPopup('Return', orderNumber, requestNumber);
            document.getElementById('returnForm').reset();
            document.getElementById('returnFilesList').innerHTML = '';

            // Switch to status section after successful submission
            setTimeout(() => {
                hideAllContentSections();
                showContentSection('status');
                const navLinks = document.querySelectorAll('.horizontal-nav-link');
                navLinks.forEach(l => l.classList.remove('active'));
                document.querySelector('.horizontal-nav-link[data-section="status"]').classList.add('active');
            }, 2000); // Wait 2 seconds to show confirmation popup first
        });

        document.getElementById('exchangeForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            if(!validateForm('exchange')) return;

            const orderNumber = document.getElementById('exchangeOrderNumber').value;
            const requestNumber = 'REQ-' + Math.floor(100000 + Math.random() * 900000);
            addStatusItem(requestNumber, 'Exchange');
            showConfirmationPopup('Exchange', orderNumber, requestNumber);
            document.getElementById('exchangeForm').reset();
            document.getElementById('exchangeFilesList').innerHTML = '';
            document.getElementById('newProductDetails').innerHTML = '';
            document.getElementById('priceDifferenceSection').innerHTML = '';

            // Switch to status section after successful submission
            setTimeout(() => {
                hideAllContentSections();
                showContentSection('status');
                const navLinks = document.querySelectorAll('.horizontal-nav-link');
                navLinks.forEach(l => l.classList.remove('active'));
                document.querySelector('.horizontal-nav-link[data-section="status"]').classList.add('active');
            }, 2000); // Wait 2 seconds to show confirmation popup first
        });
    }

    function validateForm(type) {
        const form = document.getElementById(`${type}Form`);
        const orderNumber = form.querySelector(`#${type}OrderNumber`);
        const productName = form.querySelector(`#${type}ProductName`);
        const reason = form.querySelector(`#${type}Reason`);
        const files = form.querySelector(`#${type}Files`);
        const comments = form.querySelector(`#${type}Comments`);
        const newProduct = form.querySelector('#newProductName');

        if (!orderNumber.value.trim()) {
            alert("Please enter the order number.");
            orderNumber.focus();
            return false;
        }
        if (!productName.value.trim()) {
            alert("Please enter the product name.");
            productName.focus();
            return false;
        }
        if (reason.value === "") {
            alert("Please select a reason for the request.");
            reason.focus();
            return false;
        }
        if (!comments.value.trim()) {
            alert("Please provide comments for your request.");
            comments.focus();
            return false;
        }
        if (files.files.length === 0) {
            alert("Please upload at least one image or video.");
            files.focus();
            return false;
        }
        if (type === 'exchange' && !newProduct.value) {
             alert("Please select a product to exchange for.");
             return false;
        }
        return true;
    }

    window.handleExchangeSelection = (el) => {
        const newProductName = el.dataset.name;
        const newPrice = parseFloat(el.dataset.price);

        document.getElementById('newProductName').value = newProductName;
        const newProductDetails = document.getElementById('newProductDetails');
        newProductDetails.innerHTML = `<h4>Exchanging for: ${newProductName} ($${newPrice.toFixed(2)})</h4>`;
        newProductDetails.classList.remove('hidden');
        document.getElementById('exchangeProductSelection').classList.add('hidden');

        // For now, we'll assume a default original price of $10 since we don't have order data
        // In a real application, this would be fetched from the order system
        const originalTotal = 10.00;
        const difference = newPrice - originalTotal;
        const diffSection = document.getElementById('priceDifferenceSection');
        diffSection.classList.remove('hidden');

        if (difference > 0) {
            diffSection.innerHTML = `<div class="price-difference-section">
                <strong>Amount Due: $${difference.toFixed(2)}</strong>
                <button type="button" class="submit-btn" style="width: auto; margin-left: 20px;">Pay Difference</button>
            </div>`;
        } else if (difference < 0) {
             diffSection.innerHTML = `<div class="price-difference-section">
                <strong>Refund Due: $${Math.abs(difference).toFixed(2)}</strong>
                <div class="radio-group" style="margin-top: 10px; justify-content: center; flex-direction: row;">
                    <label><input type="radio" name="exchangeRefund" value="original" checked> Original Method</label>
                    <label><input type="radio" name="exchangeRefund" value="credit"> Store Credit</label>
                </div>
            </div>`;
        } else {
            diffSection.classList.add('hidden');
        }
    }

    function initDragAndDrop(inputId, listId) {
        const fileInput = document.getElementById(inputId);
        const dropLabel = document.querySelector(`label[for="${inputId}"]`);
        const fileListDiv = document.getElementById(listId);

        if (!fileInput || !dropLabel || !fileListDiv) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropLabel.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropLabel.addEventListener(eventName, () => dropLabel.classList.add('highlight'), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropLabel.addEventListener(eventName, () => dropLabel.classList.remove('highlight'), false);
        });

        dropLabel.addEventListener('drop', handleDrop, false);
        fileInput.addEventListener('change', (e) => handleFiles(e.target.files));

        function preventDefaults(e) { e.preventDefault(); e.stopPropagation(); }
        function handleDrop(e) { fileInput.files = e.dataTransfer.files; handleFiles(fileInput.files); }
        function handleFiles(files) {
            fileListDiv.innerHTML = "";
            if (files.length > 0) {
                const list = document.createElement("ul");
                Array.from(files).forEach(file => {
                    const listItem = document.createElement("li");
                    listItem.textContent = `${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
                    list.appendChild(listItem);
                });
                fileListDiv.appendChild(list);
            }
        }
    }

    // --- SAMPLE DATA ---
    function loadSampleStatusData() {
        const container = document.getElementById('status-items-container');

        // Sample return request
        const returnCard = createSampleStatusCard('REQ-123456', 'Return', 'Bosch Spark Plug', 'ORD12345', 'Wrong Part Received', '$12.99', 'Received wrong spark plug model for my vehicle', 'Nov 15, 2023', 'completed');

        // Sample exchange request
        const exchangeCard = createSampleStatusCard('REQ-789012', 'Exchange', 'ACDelco Oil Filter', 'ORD67890', 'Incorrect Fitment', '$8.49', 'Filter does not fit my engine model', 'Nov 18, 2023', 'pending');

        // Sample recent return
        const recentCard = createSampleStatusCard('REQ-345678', 'Return', 'Monroe Shock Absorber', 'ORD11111', 'Part Damaged/Defective', '$45.99', 'Shock absorber arrived with visible damage', 'Nov 20, 2023', 'active');

        container.appendChild(returnCard);
        container.appendChild(exchangeCard);
        container.appendChild(recentCard);
    }

    function createSampleStatusCard(requestNumber, type, productName, orderNumber, reason, amount, comments, date, status) {
        const newCard = document.createElement('div');
        newCard.className = 'status-item-card';
        newCard.setAttribute('data-type', type.toLowerCase());
        newCard.setAttribute('data-status', status);
        newCard.setAttribute('data-date', date);

        let progressTrackerHTML = '';
        if (type === 'Return') {
            if (status === 'completed') {
                progressTrackerHTML = `
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${date}</div></div>
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-thumbs-up"></i></div><div class="progress-step-title">Request Approved</div><div class="progress-step-date">Nov 16, 2023</div></div>
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Nov 17, 2023</div></div>
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Quality Check</div><div class="progress-step-date">Nov 18, 2023</div></div>
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Refund Processed</div><div class="progress-step-date">Nov 19, 2023</div></div>
                `;
            } else if (status === 'active') {
                progressTrackerHTML = `
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${date}</div></div>
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-thumbs-up"></i></div><div class="progress-step-title">Request Approved</div><div class="progress-step-date">Nov 21, 2023</div></div>
                    <div class="progress-step active"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">In Progress</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Quality Check</div><div class="progress-step-date">Pending</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Refund Processed</div><div class="progress-step-date">Pending</div></div>
                `;
            } else {
                progressTrackerHTML = `
                    <div class="progress-step active"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${date}</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-thumbs-up"></i></div><div class="progress-step-title">Request Approved</div><div class="progress-step-date">Pending</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Pending</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Quality Check</div><div class="progress-step-date">Pending</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Refund Processed</div><div class="progress-step-date">Pending</div></div>
                `;
            }
        } else {
            progressTrackerHTML = `
                <div class="progress-step active"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${date}</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Approved</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Completed</div><div class="progress-step-date">Pending</div></div>
            `;
        }

        const statusBadgeClass = status === 'completed' ? 'completed' : 'pending';
        const statusText = status === 'completed' ? 'Completed' : status === 'active' ? 'In Progress' : 'Pending';

        newCard.innerHTML = `
            <div class="status-accordion-header" onclick="toggleAccordion(this)">
                <div class="status-header-content">
                    <div class="status-header-left">
                        <h3>${type} Request #${requestNumber}</h3>
                        <p>Submitted on ${date} • ${productName}</p>
                    </div>
                    <div class="status-header-right">
                        <span class="status-badge ${statusBadgeClass}">${statusText}</span>
                        <i class="fas fa-chevron-down accordion-toggle"></i>
                    </div>
                </div>
            </div>
            <div class="status-accordion-content">
                <div class="status-accordion-body">
                    <div class="status-card-product">
                        <div class="status-product-details">
                            <div><span>${productName}</span><span>Product(s)</span></div>
                            <div><span>${orderNumber}</span><span>Order #</span></div>
                            <div><span>${reason}</span><span>Reason</span></div>
                            <div><span>${amount}</span><span>Amount</span></div>
                            <div><span>${comments}</span><span>Comments</span></div>
                        </div>
                    </div>
                    <div class="progress-tracker">
                        ${progressTrackerHTML}
                    </div>
                    <div class="status-actions">
                        <button class="action-btn"><i class="fas fa-headset"></i> Contact Support</button>
                        <button class="action-btn"><i class="fas fa-times"></i> Cancel ${type}</button>
                    </div>
                </div>
            </div>
        `;

        return newCard;
    }

    // --- STATUS & CONFIRMATION ---
    function initConfirmationPopup() {
        document.getElementById('viewStatusBtn')?.addEventListener('click', () => {
            document.getElementById('confirmationPopup').style.display = 'none';
            document.getElementById('status-section').scrollIntoView();
        });
        document.getElementById('closePopupBtn')?.addEventListener('click', () => {
             document.getElementById('confirmationPopup').style.display = 'none';
        });
    }

    // --- STATUS CONTROLS (SORTING & FILTERING) ---
    function initStatusControls() {
        // Initialize sort dropdown
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', handleSortChange);
        }

        // Initialize filter buttons
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.addEventListener('click', handleFilterChange);
        });
    }

    function handleSortChange(e) {
        const sortValue = e.target.value;
        sortStatusItems(sortValue);
    }

    function handleFilterChange(e) {
        const filterValue = e.target.dataset.filter;

        // Update active filter button
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        e.target.classList.add('active');

        // Apply filter
        filterStatusItems(filterValue);
    }

    function sortStatusItems(sortType) {
        const container = document.getElementById('status-items-container');
        const items = Array.from(container.children);

        items.sort((a, b) => {
            switch(sortType) {
                case 'date-desc':
                    return new Date(getItemDate(b)) - new Date(getItemDate(a));
                case 'date-asc':
                    return new Date(getItemDate(a)) - new Date(getItemDate(b));
                case 'type-returns':
                    const aType = getItemType(a);
                    const bType = getItemType(b);
                    if (aType === bType) return new Date(getItemDate(b)) - new Date(getItemDate(a));
                    return aType === 'Return' ? -1 : 1;
                case 'type-exchanges':
                    const aTypeEx = getItemType(a);
                    const bTypeEx = getItemType(b);
                    if (aTypeEx === bTypeEx) return new Date(getItemDate(b)) - new Date(getItemDate(a));
                    return aTypeEx === 'Exchange' ? -1 : 1;
                default:
                    return 0;
            }
        });

        // Re-append sorted items
        items.forEach(item => container.appendChild(item));
    }

    function filterStatusItems(filterType) {
        const items = document.querySelectorAll('.status-item-card');

        items.forEach(item => {
            const itemStatus = getItemStatus(item);
            let shouldShow = false;

            switch(filterType) {
                case 'all':
                    shouldShow = true;
                    break;
                case 'pending':
                    shouldShow = itemStatus === 'pending';
                    break;
                case 'active':
                    shouldShow = itemStatus === 'active';
                    break;
                case 'completed':
                    shouldShow = itemStatus === 'completed';
                    break;
            }

            if (shouldShow) {
                item.classList.remove('hidden');
            } else {
                item.classList.add('hidden');
            }
        });
    }

    function getItemDate(item) {
        const dateText = item.querySelector('.status-header-left p').textContent;
        const dateMatch = dateText.match(/(\w{3} \d{1,2}, \d{4})/);
        return dateMatch ? dateMatch[1] : new Date();
    }

    function getItemType(item) {
        const titleText = item.querySelector('.status-header-left h3').textContent;
        return titleText.includes('Return') ? 'Return' : 'Exchange';
    }

    function getItemStatus(item) {
        const statusBadge = item.querySelector('.status-badge');
        if (statusBadge.classList.contains('completed')) return 'completed';
        if (statusBadge.textContent.includes('In Progress')) return 'active';
        return 'pending';
    }

    // --- ACCORDION FUNCTIONALITY ---
    window.toggleAccordion = function(header) {
        const content = header.nextElementSibling;
        const toggle = header.querySelector('.accordion-toggle');
        const isExpanded = content.classList.contains('expanded');

        if (isExpanded) {
            content.classList.remove('expanded');
            header.classList.remove('expanded');
            toggle.classList.remove('expanded');
        } else {
            content.classList.add('expanded');
            header.classList.add('expanded');
            toggle.classList.add('expanded');
        }
    };

    function addStatusItem(requestNumber, type) {
        const container = document.getElementById('status-items-container');
        const form = document.getElementById(`${type.toLowerCase()}Form`);

        const orderNumberEl = form.querySelector(`#${type.toLowerCase()}OrderNumber`);
        const productNameEl = form.querySelector(`#${type.toLowerCase()}ProductName`);
        const reasonEl = form.querySelector(`#${type.toLowerCase()}Reason`);
        const commentsEl = form.querySelector(`#${type.toLowerCase()}Comments`);

        const orderNumber = orderNumberEl.value.trim();
        const productName = productNameEl.value.trim();
        const reasonText = reasonEl.options[reasonEl.selectedIndex].text;
        const commentsText = commentsEl.value.trim();
        const totalAmount = 10.00; // Default amount since we don't have order data
        const today = new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });

        const newCard = document.createElement('div');
        newCard.className = 'status-item-card';
        newCard.setAttribute('data-type', type.toLowerCase());
        newCard.setAttribute('data-status', 'pending');
        newCard.setAttribute('data-date', today);

        let progressTrackerHTML = '';
        if (type === 'Return') {
            progressTrackerHTML = `
                <div class="progress-step active"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${today}</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-thumbs-up"></i></div><div class="progress-step-title">Request Approved</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Quality Check</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Refund Processed</div><div class="progress-step-date">Pending</div></div>
            `;
        } else {
             progressTrackerHTML = `
                <div class="progress-step active"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${today}</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Approved</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Completed</div><div class="progress-step-date">Pending</div></div>
            `;
        }

        newCard.innerHTML = `
            <div class="status-accordion-header" onclick="toggleAccordion(this)">
                <div class="status-header-content">
                    <div class="status-header-left">
                        <h3>${type} Request #${requestNumber}</h3>
                        <p>Submitted on ${today} • ${productName}</p>
                    </div>
                    <div class="status-header-right">
                        <span class="status-badge pending">Pending</span>
                        <i class="fas fa-chevron-down accordion-toggle"></i>
                    </div>
                </div>
            </div>
            <div class="status-accordion-content">
                <div class="status-accordion-body">
                    <div class="status-card-product">
                        <div class="status-product-details">
                            <div><span>${productName}</span><span>Product(s)</span></div>
                            <div><span>${orderNumber}</span><span>Order #</span></div>
                            <div><span>${reasonText}</span><span>Reason</span></div>
                            <div><span>$${totalAmount.toFixed(2)}</span><span>Amount</span></div>
                            <div><span>${commentsText}</span><span>Comments</span></div>
                        </div>
                    </div>
                    <div class="progress-tracker">
                        ${progressTrackerHTML}
                    </div>
                    <div class="status-actions">
                        <button class="action-btn"><i class="fas fa-headset"></i> Contact Support</button>
                        <button class="action-btn"><i class="fas fa-times"></i> Cancel ${type}</button>
                    </div>
                </div>
            </div>
        `;
        container.prepend(newCard);
        updateDashboard(type, 1);

        // Apply current sort and filter to new item
        const currentSort = document.getElementById('sortSelect').value;
        const currentFilter = document.querySelector('.filter-btn.active').dataset.filter;
        sortStatusItems(currentSort);
        filterStatusItems(currentFilter);
    }

    function showConfirmationPopup(type, orderNumber, requestNumber) {
        document.getElementById('requestType').textContent = type;
        document.getElementById('confirmationOrderNumber').textContent = orderNumber;
        document.getElementById('requestNumber').textContent = requestNumber;
        document.getElementById('confirmationPopup').style.display = 'flex';
    }
    </script>
</body>
</html>
