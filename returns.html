<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Returns & Exchanges - Portal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #3C91FF; 
            --text-color-light: #333A3D;
            --secondary-color: #ff5722;
            --background-color-light: #f8f9fa;
            --border-color-light: #dee2e6;
            --card-bg-light: #ffffff;
            --success-color: #28a745;
            --sidebar-width: 240px;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            background-color: var(--background-color-light);
            color: var(--text-color-light);
        }

        .app-container {
            display: flex;
        }

        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            background-color: var(--card-bg-light);
            border-right: 1px solid var(--border-color-light);
            padding-top: 40px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .sidebar-nav ul {
            list-style: none;
            padding: 0 15px;
            margin: 0;
        }

        .sidebar-nav li a {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            text-decoration: none;
            color: var(--text-color-light);
            font-weight: 600;
            border-radius: 8px;
            margin-bottom: 5px;
            transition: background-color 0.2s, color 0.2s;
        }
        .sidebar-nav li a i {
            margin-right: 15px;
            width: 20px;
            text-align: center;
        }

        .sidebar-nav li a:hover {
            background-color: var(--background-color-light);
        }

        .sidebar-nav li a.active {
            background-color: var(--primary-color);
            color: white;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            width: calc(100% - var(--sidebar-width));
            padding: 40px;
            box-sizing: border-box;
        }

        .content-section {
            margin-bottom: 50px;
            scroll-margin-top: 20px;
        }
        
        h1 { font-size: 2rem; font-weight: 700; margin-bottom: 10px; color: var(--primary-color); }
        .section-description { font-size: 1rem; color: #6c757d; margin-bottom: 2rem; }
        
        /* Dashboard */
        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background-color: var(--card-bg-light);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .stat-card .icon {
            font-size: 2rem;
            color: var(--primary-color);
        }
        .stat-card .info .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
        }
         .stat-card .info .stat-title {
            font-size: 0.9rem;
            color: #6c757d;
        }


        /* Order History */
        .order-item {
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            margin-bottom: 20px;
            background-color: var(--card-bg-light);
        }
        .order-header { padding: 20px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid var(--border-color-light); }
        .order-info h3 { margin: 0; font-size: 1.2rem; }
        .order-date { font-size: 0.9rem; color: #6c757d; }
        .status-badge { padding: 5px 12px; border-radius: 20px; font-weight: 600; font-size: 0.8rem; }
        .status-badge.delivered { background-color: #d4edda; color: #155724; }
        .status-badge.pending { background-color: #fff3cd; color: #856404; }

        .order-products { padding: 20px; }
        .order-product { display: flex; align-items: center; gap: 20px; padding: 15px; border-radius: 8px; transition: background-color .2s; }
        .order-product.selected { background-color: #f0f7ff; }
        .product-details { flex-grow: 1; }
        .product-details h4 { margin: 0 0 10px 0; }
        .product-info { display: flex; gap: 20px; font-size: 0.9rem; }
        .product-action-btn { background: none; border: 1px solid var(--border-color-light); border-radius: 5px; padding: 8px 12px; cursor: pointer; font-weight: 600; }
        .product-action-btn:hover { background-color: var(--background-color-light); }
        .bulk-action-container { display:flex; align-items:center; gap: 10px; margin-bottom: 20px; }
        .selected-actions { display: flex; align-items: center; gap: 10px; }
        .hidden { display: none !important; }

        /* Forms */
        .form-card {
            background-color: var(--card-bg-light);
            padding: 30px;
            border-radius: 8px;
            border: 1px solid var(--border-color-light);
        }
        .tab-buttons { display: flex; border-bottom: 1px solid var(--border-color-light); margin-bottom: 2rem; }
        .tab-btn { background: none; border: none; padding: 15px 20px; font-size: 1.1rem; font-weight: 600; cursor: pointer; border-bottom: 3px solid transparent; }
        .tab-btn.active { border-bottom-color: var(--primary-color); color: var(--primary-color); }
        .tab-pane { display: none; }
        .tab-pane.active { display: block; }
        .form-group { margin-bottom: 1.5rem; }
        .form-group label { display: block; font-weight: 600; margin-bottom: 8px; }
        select, textarea {
            width: 100%; padding: 12px; border: 1px solid var(--border-color-light); border-radius: 5px; box-sizing: border-box;
        }
        .submit-btn {
            background-color: var(--primary-color); color: white; border: none; padding: 15px 25px; border-radius: 5px; font-size: 1rem; font-weight: 600; cursor: pointer; width: 100%; margin-top: 10px;
        }
        .item-details-content { border: 1px solid var(--border-color-light); border-radius: 8px; padding: 15px; min-height: 50px; }
        .radio-group-label { font-weight: 600; margin-bottom: 8px; display:block; }
        .radio-group { display: flex; flex-direction: column; gap: 10px; }
        .radio-group label { display: flex; align-items: center; gap: 10px; font-weight: 500; }
        
        /* Exchange Product Selection */
        .exchange-product-selection {
            background-color: var(--background-color-light);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 20px;
            margin-top: 10px;
        }
        .exchange-product-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .exchange-product-item:hover {
            background-color: var(--border-color-light);
        }
        .price-difference-section {
            padding: 15px;
            border: 1px solid var(--primary-color);
            background-color: #f0f7ff;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
        }

        /* Drag and Drop File Upload */
        .file-upload-label {
            display: flex; flex-direction: column; align-items: center; justify-content: center;
            padding: 30px; border: 2px dashed var(--border-color-light); border-radius: 8px;
            cursor: pointer; text-align: center; transition: background-color .2s, border-color .2s;
        }
        .file-upload-label.highlight {
            border-color: var(--primary-color);
            background-color: #f0f7ff;
        }
        .file-upload-label i { font-size: 2rem; color: var(--primary-color); margin-bottom: 10px;}
        .file-list { margin-top: 10px; font-size: 0.9rem; }
        .file-list ul { list-style-type: none; padding: 0;}
        .file-list li { background-color: #f1f1f1; padding: 5px 10px; border-radius: 4px; margin-top: 5px; }


        /* Success Popup */
        .success-popup {
            display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.6); z-index: 1000;
            align-items: center; justify-content: center;
        }
        .confirmation-content { background-color: var(--card-bg-light); padding: 40px; border-radius: 10px; text-align: center; max-width: 500px; }
        .confirmation-icon i { font-size: 60px; color: var(--success-color); margin-bottom: 20px; }
        .confirmation-details { margin: 30px 0; text-align: left; }
        .confirmation-detail { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid var(--border-color-light); }
        .confirmation-btn { padding: 12px 20px; border-radius: 5px; border: none; cursor: pointer; font-weight: 600; }
        .confirmation-btn.secondary { background-color: var(--border-color-light); }

        /* STATUS PAGE - NEW STYLES */
        .status-items-container { display: flex; flex-direction: column; gap: 2rem; }
        .status-item-card { border: 1px solid var(--border-color-light); border-radius: 8px; padding: 25px; background-color: var(--card-bg-light); }
        .status-card-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;}
        .status-card-header h3 { margin: 0; font-size: 1.3rem; }
        .status-card-header p { margin: 5px 0 0; color: #6c757d; }
        .status-card-product { display: flex; align-items: center; gap: 20px; border-bottom: 1px solid var(--border-color-light); padding-bottom: 20px; margin-bottom: 20px; }
        .status-product-details { display: flex; flex-wrap: wrap; gap: 10px 30px; flex-grow: 1; }
        .status-product-details > div { display: flex; flex-direction: column; }
        .status-product-details span:first-child { font-weight: 600; }
        .status-product-details span:last-child { color: #6c757d; }

        .progress-tracker { display: flex; justify-content: space-between; position: relative; margin: 30px 0; }
        .progress-tracker::before {
            content: '';
            position: absolute;
            top: 15px; /* (icon height / 2) */
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--border-color-light);
            z-index: 1;
        }
        .progress-step { display: flex; flex-direction: column; align-items: center; text-align: center; width: 120px; position: relative; z-index: 2; }
        .progress-icon {
            width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center;
            background-color: var(--card-bg-light); border: 2px solid var(--border-color-light);
            margin-bottom: 10px;
        }
        .progress-icon i { font-size: 14px; }
        .progress-step-title { font-weight: 600; font-size: 0.9rem; }
        .progress-step-date { font-size: 0.8rem; color: #6c757d; }

        .progress-step.completed .progress-icon { border-color: var(--success-color); background-color: var(--success-color); color: white; }
        .progress-step.active .progress-icon { border-color: var(--primary-color); background-color: var(--primary-color); color: white; }
        .progress-step.active .progress-step-title { color: var(--primary-color); }

        .status-actions { display: flex; gap: 15px; margin-top: 20px; }
        .status-actions .action-btn { background-color: transparent; border: 1px solid var(--border-color-light); padding: 10px 15px; border-radius: 5px; font-weight: 600; cursor: pointer; }
        .status-actions .action-btn.primary { background-color: var(--primary-color); color: white; border-color: var(--primary-color); }
    </style>

</head>
<body>
    <div class="app-container">
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="#dashboard-section" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="#orders-section"><i class="fas fa-box"></i> Your Orders</a></li>
                    <li><a href="#request-section"><i class="fas fa-plus-circle"></i> New Request</a></li>
                    <li><a href="#status-section"><i class="fas fa-tasks"></i> Status</a></li>
                </ul>
            </nav>
        </aside>

        <main class="main-content">
            <section id="dashboard-section" class="content-section">
                <h1>Dashboard</h1>
                <div class="dashboard-container">
                    <div class="stat-card">
                        <div class="icon"><i class="fas fa-shopping-cart"></i></div>
                        <div class="info">
                            <div id="orders-count" class="stat-value">0</div>
                            <div class="stat-title">Total Orders</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="icon"><i class="fas fa-undo-alt"></i></div>
                        <div class="info">
                            <div id="returned-count" class="stat-value">0</div>
                            <div class="stat-title">Items Returned</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="icon"><i class="fas fa-exchange-alt"></i></div>
                        <div class="info">
                            <div id="exchanged-count" class="stat-value">0</div>
                            <div class="stat-title">Items Exchanged</div>
                        </div>
                    </div>
                </div>
            </section>
        
            <section id="orders-section" class="content-section">
                <h1>Your Orders</h1>
                <p class="section-description">View and manage your recent orders to initiate a return or exchange.</p>
                 <div class="bulk-action-container">
                    <button id="selectAllBtn" class="product-action-btn"><i class="fas fa-check-square"></i> Select All</button>
                    <button id="deselectAllBtn" class="product-action-btn"><i class="fas fa-square"></i> Deselect All</button>
                    <div class="selected-actions hidden">
                        <span class="selected-count">0 items selected</span>
                        <button id="bulkReturnBtn" class="product-action-btn"><i class="fas fa-undo-alt"></i> Return Selected</button>
                        <button id="bulkExchangeBtn" class="product-action-btn"><i class="fas fa-exchange-alt"></i> Exchange Selected</button>
                    </div>
                </div>
                <div class="order-list">
                    <!-- Order 1 -->
                    <div class="order-item">
                        <div class="order-header">
                            <div class="order-info"><h3>Order #ORD12345</h3><span class="order-date">Ordered on October 15, 2023</span></div>
                            <div class="order-status"><span class="status-badge delivered">Delivered</span></div>
                        </div>
                        <div class="order-products">
                            <div class="order-product">
                                <div class="product-select"><input type="checkbox" class="product-checkbox" data-order-id="ORD12345" data-product-id="P123456"></div>
                                <div class="product-details">
                                    <h4>Bosch Spark Plug</h4>
                                    <div class="product-info"><span><strong>Part #:</strong> SP-9876-B4</span><span><strong>Price:</strong> $12.99</span></div>
                                </div>
                                <div class="product-actions">
                                    <button class="product-action-btn" onclick="navigateToRequest('return', 'ORD12345', 'P123456')"><i class="fas fa-undo-alt"></i> Return</button>
                                    <button class="product-action-btn" onclick="navigateToRequest('exchange', 'ORD12345', 'P123456')"><i class="fas fa-exchange-alt"></i> Exchange</button>
                                </div>
                            </div>
                            <div class="order-product">
                                <div class="product-select"><input type="checkbox" class="product-checkbox" data-order-id="ORD12345" data-product-id="P789012"></div>
                                <div class="product-details">
                                    <h4>ACDelco Oil Filter</h4>
                                    <div class="product-info"><span><strong>Part #:</strong> PF-456</span><span><strong>Price:</strong> $8.49</span></div>
                                </div>
                                <div class="product-actions">
                                    <button class="product-action-btn" onclick="navigateToRequest('return', 'ORD12345', 'P789012')"><i class="fas fa-undo-alt"></i> Return</button>
                                    <button class="product-action-btn" onclick="navigateToRequest('exchange', 'ORD12345', 'P789012')"><i class="fas fa-exchange-alt"></i> Exchange</button>
                                </div>
                            </div>
                        </div>
                    </div>
                     <!-- Order 2 -->
                    <div class="order-item">
                        <div class="order-header">
                            <div class="order-info"><h3>Order #ORD67890</h3><span class="order-date">Ordered on November 2, 2023</span></div>
                            <div class="order-status"><span class="status-badge delivered">Delivered</span></div>
                        </div>
                        <div class="order-products">
                            <div class="order-product">
                                <div class="product-select"><input type="checkbox" class="product-checkbox" data-order-id="ORD67890" data-product-id="P345678"></div>
                                <div class="product-details">
                                    <h4>Monroe Shock Absorber</h4>
                                    <div class="product-info"><span><strong>Part #:</strong> SA-3456-R</span><span><strong>Price:</strong> $45.99</span></div>
                                </div>
                                <div class="product-actions">
                                    <button class="product-action-btn" onclick="navigateToRequest('return', 'ORD67890', 'P345678')"><i class="fas fa-undo-alt"></i> Return</button>
                                    <button class="product-action-btn" onclick="navigateToRequest('exchange', 'ORD67890', 'P345678')"><i class="fas fa-exchange-alt"></i> Exchange</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="request-section" class="content-section">
                <h1>New Return or Exchange Request</h1>
                <p class="section-description">Select an item from your orders above, or start a new request here.</p>
                <div class="form-card">
                    <div class="tab-buttons">
                        <button class="tab-btn active" data-tab="return">Return</button>
                        <button class="tab-btn" data-tab="exchange">Exchange</button>
                    </div>
                    <div class="tab-content">
                        <div id="return" class="tab-pane active">
                            <form id="returnForm">
                                <div class="form-group"><label>Item(s) to Return</label><div class="item-details-content" id="itemDetails"><p>Select an item from an order to begin.</p></div></div>
                                <div class="form-group">
                                    <label for="returnReason">Reason for Return*</label>
                                    <select id="returnReason" required>
                                        <option value="">Select a reason...</option>
                                        <option value="wrong-part">Wrong Part Received</option>
                                        <option value="damaged">Part Damaged/Defective</option>
                                        <option value="not-as-described">Not as Described</option>
                                        <option value="no-longer-needed">No Longer Needed</option>
                                        <option value="better-price">Found Better Price Elsewhere</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                 <div class="form-group">
                                    <label>Upload Image/Video Evidence*</label>
                                    <div class="file-upload-container">
                                        <label for="returnFiles" class="file-upload-label">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <span>Choose files or drag & drop</span>
                                        </label>
                                        <input type="file" id="returnFiles" class="hidden" multiple required>
                                        <div id="returnFilesList" class="file-list"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <span class="radio-group-label">Return Method*</span>
                                    <div class="radio-group">
                                        <label><input type="radio" name="returnMethod" value="courier" checked> Courier Pickup</label>
                                        <label><input type="radio" name="returnMethod" value="store"> Return to Store</label>
                                        <label><input type="radio" name="returnMethod" value="warehouse"> Warehouse Drop-off</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <span class="radio-group-label">Refund Method*</span>
                                    <div class="radio-group">
                                        <label><input type="radio" name="refundMethod" value="original" checked> Original Payment Method</label>
                                        <label><input type="radio" name="refundMethod" value="credit"> Store Credit</label>
                                    </div>
                                </div>
                                <button type="submit" class="submit-btn">Submit Return Request</button>
                            </form>
                        </div>
                        <div id="exchange" class="tab-pane">
                            <form id="exchangeForm">
                                <div class="form-group"><label>Item(s) to Exchange</label><div class="item-details-content" id="exchangeItemDetails"><p>Select an item from an order to begin.</p></div></div>
                                <div class="form-group">
                                    <label for="exchangeReason">Reason for Exchange*</label>
                                    <select id="exchangeReason" required>
                                        <option value="">Select a reason...</option>
                                        <option value="wrong-part">Wrong Part Received</option>
                                        <option value="incorrect-fitment">Incorrect Fitment</option>
                                        <option value="damaged">Part Damaged/Defective</option>
                                        <option value="upgrade">Upgrade/Different Part</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                 <div class="form-group">
                                    <label>Upload Image/Video Evidence*</label>
                                     <div class="file-upload-container">
                                        <label for="exchangeFiles" class="file-upload-label">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <span>Choose files or drag & drop</span>
                                        </label>
                                        <input type="file" id="exchangeFiles" class="hidden" multiple required>
                                        <div id="exchangeFilesList" class="file-list"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Choose New Product*</label>
                                    <div class="exchange-product-selection" id="exchangeProductSelection">
                                        <!-- Mock product list -->
                                        <div class="exchange-product-item" onclick="handleExchangeSelection(this)" data-name="Premium Spark Plug" data-price="19.99">
                                            <span>Premium Spark Plug</span><strong>$19.99</strong>
                                        </div>
                                         <div class="exchange-product-item" onclick="handleExchangeSelection(this)" data-name="Performance Oil Filter" data-price="15.49">
                                            <span>Performance Oil Filter</span><strong>$15.49</strong>
                                        </div>
                                        <div class="exchange-product-item" onclick="handleExchangeSelection(this)" data-name="Standard Wiper Blade" data-price="7.99">
                                            <span>Standard Wiper Blade</span><strong>$7.99</strong>
                                        </div>
                                    </div>
                                    <div class="item-details-content hidden" id="newProductDetails"></div>
                                    <input type="hidden" id="newProductName" name="newProductName" required>
                                </div>
                                <div id="priceDifferenceSection" class="hidden"></div>
                                <button type="submit" class="submit-btn">Submit Exchange Request</button>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
            
            <section id="status-section" class="content-section">
                <h1>Request Status</h1>
                <p class="section-description">Track the progress of your submitted return and exchange requests.</p>
                <div id="status-items-container" class="status-items-container">
                    <!-- Status items will be added here by JavaScript -->
                </div>
            </section>
        </main>
    </div>

    <div id="confirmationPopup" class="success-popup">
        <div class="confirmation-content">
            <div class="confirmation-icon"><i class="fas fa-check-circle"></i></div>
            <h3>Request Submitted!</h3>
            <div class="confirmation-details">
                <div class="confirmation-detail"><span class="detail-label">Request #:</span><span class="detail-value" id="requestNumber"></span></div>
                <div class="confirmation-detail"><span class="detail-label">Order #:</span><span class="detail-value" id="confirmationOrderNumber"></span></div>
                <div class="confirmation-detail"><span class="detail-label">Type:</span><span class="detail-value" id="requestType"></span></div>
            </div>
            <div class="confirmation-actions">
                <button id="viewStatusBtn" class="confirmation-btn">View Status</button>
                <button id="closePopupBtn" class="confirmation-btn secondary">Close</button>
            </div>
        </div>
    </div>


    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // --- INITIALIZATION ---
        initSidebarNav();
        initOrdersPage();
        initRequestForms();
        initConfirmationPopup();
        updateDashboard();
    });

    // --- DASHBOARD ---
    function updateDashboard(type, count) {
        if(type === 'Return') {
            const returnedEl = document.getElementById('returned-count');
            returnedEl.textContent = parseInt(returnedEl.textContent) + count;
        } else if (type === 'Exchange') {
            const exchangedEl = document.getElementById('exchanged-count');
            exchangedEl.textContent = parseInt(exchangedEl.textContent) + count;
        }
        // Initial call
        if(!type) {
            document.getElementById('orders-count').textContent = document.querySelectorAll('.order-item').length;
        }
    }

    // --- SIDEBAR & SCROLLSPY ---
    function initSidebarNav() {
        const sections = document.querySelectorAll('.content-section');
        const navLinks = document.querySelectorAll('.sidebar-nav a');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const id = entry.target.id;
                    navLinks.forEach(link => {
                        link.classList.toggle('active', link.getAttribute('href') === '#' + id);
                    });
                }
            });
        }, { rootMargin: '-50% 0px -50% 0px' });

        sections.forEach(section => observer.observe(section));
    }
    
    // --- NAVIGATION HELPERS ---
    function switchTab(type) {
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        document.querySelector(`.tab-btn[data-tab="${type}"]`).classList.add('active');
        document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
        document.getElementById(type).classList.add('active');
    }

    window.navigateToRequest = (type, orderId, productId) => {
        document.getElementById('request-section').scrollIntoView();
        switchTab(type);
        const products = productId ? [{orderId, productId}] : JSON.parse(localStorage.getItem('selectedProducts'));
        
        if (type === 'return') populateReturnForm(products);
        else populateExchangeForm(products);
    };
    
    // --- ORDERS PAGE ---
    function initOrdersPage() {
        document.querySelectorAll('.order-product .product-action-btn').forEach(btn => btn.disabled = false);
        document.querySelectorAll('.order-product').forEach(container => {
            const checkbox = container.querySelector('.product-checkbox');
            if (checkbox) {
                container.addEventListener('click', (e) => {
                    if (!e.target.closest('button')) {
                        checkbox.checked = !checkbox.checked;
                        updateSelectionState();
                    }
                });
            }
        });
        document.getElementById('selectAllBtn')?.addEventListener('click', () => {
            document.querySelectorAll('.product-checkbox').forEach(cb => cb.checked = true);
            updateSelectionState();
        });
        document.getElementById('deselectAllBtn')?.addEventListener('click', () => {
            document.querySelectorAll('.product-checkbox').forEach(cb => cb.checked = false);
            updateSelectionState();
        });
        document.getElementById('bulkReturnBtn')?.addEventListener('click', () => handleBulkAction('return'));
        document.getElementById('bulkExchangeBtn')?.addEventListener('click', () => handleBulkAction('exchange'));
    }
    
    function updateSelectionState() {
        const selected = getSelectedProducts();
        document.querySelector('.selected-count').textContent = `${selected.length} item(s) selected`;
        document.querySelector('.selected-actions').classList.toggle('hidden', selected.length === 0);
        document.querySelectorAll('.order-product').forEach(p => {
            const checkbox = p.querySelector('.product-checkbox');
            if(checkbox) p.classList.toggle('selected', checkbox.checked);
        });
    }

    function getSelectedProducts() {
        const selected = [];
        document.querySelectorAll('.product-checkbox:checked').forEach(cb => {
            selected.push({ orderId: cb.dataset.orderId, productId: cb.dataset.productId });
        });
        return selected;
    }

    function handleBulkAction(action) {
        const products = getSelectedProducts();
        if (products.length === 0) return;
        localStorage.setItem('selectedProducts', JSON.stringify(products));
        navigateToRequest(action);
    }
    
    // --- REQUEST FORMS ---
    function initRequestForms() {
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.target.dataset.tab;
                switchTab(tabId);
                localStorage.removeItem('selectedProducts');
            });
        });

        initDragAndDrop('returnFiles', 'returnFilesList');
        initDragAndDrop('exchangeFiles', 'exchangeFilesList');

        document.getElementById('returnForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            if(!validateForm('return')) return;
            const items = document.querySelectorAll('#itemDetails .selected-item');
            if (items.length === 0) { alert("Please select an item from an order first."); return; }
            const orderNumber = items[0].dataset.orderId;

            const requestNumber = 'REQ-' + Math.floor(100000 + Math.random() * 900000);
            addStatusItem(requestNumber, 'Return');
            showConfirmationPopup('Return', orderNumber, requestNumber);
            document.getElementById('returnForm').reset();
            document.getElementById('itemDetails').innerHTML = '<p>Select an item from an order to begin.</p>';
            document.getElementById('returnFilesList').innerHTML = '';
        });
        
        document.getElementById('exchangeForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
             if(!validateForm('exchange')) return;
            const items = document.querySelectorAll('#exchangeItemDetails .selected-item');
            if (items.length === 0) { alert("Please select an item from an order first."); return; }
            const orderNumber = items[0].dataset.orderId;

            const requestNumber = 'REQ-' + Math.floor(100000 + Math.random() * 900000);
            addStatusItem(requestNumber, 'Exchange');
            showConfirmationPopup('Exchange', orderNumber, requestNumber);
            document.getElementById('exchangeForm').reset();
            document.getElementById('exchangeItemDetails').innerHTML = '<p>Select an item from an order to begin.</p>';
            document.getElementById('exchangeFilesList').innerHTML = '';
            document.getElementById('newProductDetails').innerHTML = '';
            document.getElementById('priceDifferenceSection').innerHTML = '';
        });
    }

    function validateForm(type) {
        const form = document.getElementById(`${type}Form`);
        const reason = form.querySelector(`#${type}Reason`);
        const files = form.querySelector(`#${type}Files`);
        const newProduct = form.querySelector('#newProductName');

        if (reason.value === "") {
            alert("Please select a reason for the request.");
            reason.focus();
            return false;
        }
        if (files.files.length === 0) {
            alert("Please upload at least one image or video.");
            files.focus();
            return false;
        }
        if (type === 'exchange' && !newProduct.value) {
             alert("Please select a product to exchange for.");
             return false;
        }
        return true;
    }

    window.handleExchangeSelection = (el) => {
        const newProductName = el.dataset.name;
        const newPrice = parseFloat(el.dataset.price);

        document.getElementById('newProductName').value = newProductName;
        const newProductDetails = document.getElementById('newProductDetails');
        newProductDetails.innerHTML = `<h4>Exchanging for: ${newProductName} ($${newPrice.toFixed(2)})</h4>`;
        newProductDetails.classList.remove('hidden');
        document.getElementById('exchangeProductSelection').classList.add('hidden');

        let originalTotal = 0;
        document.querySelectorAll('#exchangeItemDetails .selected-item').forEach(item => {
            originalTotal += parseFloat(item.dataset.price.replace('$', ''));
        });

        const difference = newPrice - originalTotal;
        const diffSection = document.getElementById('priceDifferenceSection');
        diffSection.classList.remove('hidden');

        if (difference > 0) {
            diffSection.innerHTML = `<div class="price-difference-section">
                <strong>Amount Due: $${difference.toFixed(2)}</strong>
                <button type="button" class="submit-btn" style="width: auto; margin-left: 20px;">Pay Difference</button>
            </div>`;
        } else if (difference < 0) {
             diffSection.innerHTML = `<div class="price-difference-section">
                <strong>Refund Due: $${Math.abs(difference).toFixed(2)}</strong>
                <div class="radio-group" style="margin-top: 10px; justify-content: center; flex-direction: row;">
                    <label><input type="radio" name="exchangeRefund" value="original" checked> Original Method</label>
                    <label><input type="radio" name="exchangeRefund" value="credit"> Store Credit</label>
                </div>
            </div>`;
        } else {
            diffSection.classList.add('hidden');
        }
    }

    function initDragAndDrop(inputId, listId) {
        const fileInput = document.getElementById(inputId);
        const dropLabel = document.querySelector(`label[for="${inputId}"]`);
        const fileListDiv = document.getElementById(listId);

        if (!fileInput || !dropLabel || !fileListDiv) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropLabel.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropLabel.addEventListener(eventName, () => dropLabel.classList.add('highlight'), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropLabel.addEventListener(eventName, () => dropLabel.classList.remove('highlight'), false);
        });

        dropLabel.addEventListener('drop', handleDrop, false);
        fileInput.addEventListener('change', (e) => handleFiles(e.target.files));

        function preventDefaults(e) { e.preventDefault(); e.stopPropagation(); }
        function handleDrop(e) { fileInput.files = e.dataTransfer.files; handleFiles(fileInput.files); }
        function handleFiles(files) {
            fileListDiv.innerHTML = "";
            if (files.length > 0) {
                const list = document.createElement("ul");
                Array.from(files).forEach(file => {
                    const listItem = document.createElement("li");
                    listItem.textContent = `${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
                    list.appendChild(listItem);
                });
                fileListDiv.appendChild(list);
            }
        }
    }

    function populateForm(type, products) {
        const detailsDivId = type === 'return' ? 'itemDetails' : 'exchangeItemDetails';
        const detailsDiv = document.getElementById(detailsDivId);
        
        let html = '';
        products.forEach((p) => {
            const productEl = document.querySelector(`.product-checkbox[data-order-id="${p.orderId}"][data-product-id="${p.productId}"]`)?.closest('.order-product');
            if (productEl) {
                const name = productEl.querySelector('h4').textContent;
                const priceText = productEl.querySelector('.product-info span:last-child').textContent;
                const price = priceText.replace('Price: ', '');
                html += `<div class="selected-item" data-name="${name}" data-price="${price}" data-order-id="${p.orderId}" style="padding-bottom: 10px;">
                    <h4>${name} (Order: ${p.orderId})</h4>
                </div>`;
            }
        });
        detailsDiv.innerHTML = html;
        localStorage.removeItem('selectedProducts'); // Clear after use
        
        // Reset exchange product section when populating
        if (type === 'exchange') {
            document.getElementById('newProductDetails').innerHTML = '';
            document.getElementById('newProductDetails').classList.add('hidden');
            document.getElementById('exchangeProductSelection').classList.remove('hidden');
            document.getElementById('priceDifferenceSection').classList.add('hidden');
            document.getElementById('newProductName').value = '';
        }
    }

    function populateReturnForm(products) { populateForm('return', products); }
    function populateExchangeForm(products) { populateForm('exchange', products); }
    
    // --- STATUS & CONFIRMATION ---
    function initConfirmationPopup() {
        document.getElementById('viewStatusBtn')?.addEventListener('click', () => {
            document.getElementById('confirmationPopup').style.display = 'none';
            document.getElementById('status-section').scrollIntoView();
        });
        document.getElementById('closePopupBtn')?.addEventListener('click', () => {
             document.getElementById('confirmationPopup').style.display = 'none';
        });
    }

    function addStatusItem(requestNumber, type) {
        const container = document.getElementById('status-items-container');
        const form = document.getElementById(`${type.toLowerCase()}Form`);
        const itemContainer = form.querySelector(`#${type.toLowerCase()}ItemDetails`);
        
        if (!itemContainer) {
            console.error('Could not find item container for', type);
            return;
        }
        const items = itemContainer.querySelectorAll('.selected-item');
        
        if (items.length === 0) return;
        
        const productName = items.length > 1 ? `${items.length} Items` : items[0].dataset.name;
        const orderNumber = items[0].dataset.orderId;
        let totalAmount = 0;
        items.forEach(item => totalAmount += parseFloat(item.dataset.price.replace('$', '')));

        const reasonEl = form.querySelector(`#${type.toLowerCase()}Reason`);
        const reasonText = reasonEl.options[reasonEl.selectedIndex].text;
        const today = new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });

        const newCard = document.createElement('div');
        newCard.className = 'status-item-card';
        
        let progressTrackerHTML = '';
        if (type === 'Return') {
            progressTrackerHTML = `
                <div class="progress-step active"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${today}</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-thumbs-up"></i></div><div class="progress-step-title">Request Approved</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Quality Check</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Refund Processed</div><div class="progress-step-date">Pending</div></div>
            `;
        } else {
             progressTrackerHTML = `
                <div class="progress-step active"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${today}</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Approved</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Completed</div><div class="progress-step-date">Pending</div></div>
            `;
        }

        newCard.innerHTML = `
            <div class="status-card-header">
                <div><h3>${type} Request #${requestNumber}</h3><p>Submitted on ${today}</p></div>
                <span class="status-badge pending">Pending</span>
            </div>
            <div class="status-card-product">
                <div class="status-product-details">
                    <div><span>${productName}</span><span>Product(s)</span></div>
                    <div><span>${orderNumber}</span><span>Order #</span></div>
                    <div><span>${reasonText}</span><span>Reason</span></div>
                    <div><span>$${totalAmount.toFixed(2)}</span><span>Amount</span></div>
                </div>
            </div>
            <div class="progress-tracker">
                ${progressTrackerHTML}
            </div>
            <div class="status-actions">
                <button class="action-btn"><i class="fas fa-headset"></i> Contact Support</button>
                <button class="action-btn"><i class="fas fa-times"></i> Cancel ${type}</button>
            </div>
        `;
        container.prepend(newCard);
        updateDashboard(type, items.length);
    }

    function showConfirmationPopup(type, orderNumber, requestNumber) {
        document.getElementById('requestType').textContent = type;
        document.getElementById('confirmationOrderNumber').textContent = orderNumber;
        document.getElementById('requestNumber').textContent = requestNumber;
        document.getElementById('confirmationPopup').style.display = 'flex';
    }
    </script>
</body>
</html>
