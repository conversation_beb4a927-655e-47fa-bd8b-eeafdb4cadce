<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Returns & Exchanges - Portal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2EC0CB ;
            --text-color-light: #333A3D;
            --secondary-color: #ff5722;
            --background-color-light: #f8f9fa;
            --border-color-light: #dee2e6;
            --card-bg-light: #ffffff;
            --success-color: #28a745;
            --sidebar-width: 240px;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background-color-light);
            color: var(--text-color-light);
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
        }



        .top-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color-light);
            flex-shrink: 0;
            gap: 20px;
        }

        .dashboard-area {
            flex: 1;
            min-width: 540px;
        }

        .navigation-area {
            flex: 0 0 auto;
        }

        .policy-buttons-area {
            flex: 0 0 auto;
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .bottom-section {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .content-area {
            width: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-section-container {
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            overflow-y: auto;
            display: none;
        }

        .content-section-container.active {
            display: block;
        }

        .status-section {
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            overflow-y: auto;
            display: none;
        }

        .status-section.active {
            display: block;
        }

        .content-section {
            margin-bottom: 50px;
            scroll-margin-top: 20px;
        }

        h1 { font-size: 1.8rem; font-weight: 700; margin-bottom: 5px; color: var(--primary-color); }
        .section-description { font-size: 0.95rem; color: #6c757d; margin-bottom: 1rem; }

        /* Dashboard specific heading adjustments */
        #dashboard-section h1 {
            font-size: 1.4rem;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        /* Dashboard */
        .dashboard-container {
            display: flex;
            gap: 15px;
            margin-bottom: 0;
            align-items: center;
            height: 44px;
        }
        .stat-card {
            background-color: var(--card-bg-light);
            padding: 12px 15px;
            border-radius: 6px;
            border: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            gap: 12px;
            width: 180px;
            height: 44px;
            min-width: 160px;
            box-sizing: border-box;
        }
        .stat-card .icon {
            font-size: 1.3rem;
            color: var(--primary-color);
            flex-shrink: 0;
        }
        .stat-card .info {
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-width: 0;
        }
        .stat-card .info .stat-value {
            font-size: 1.2rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 2px;
        }
         .stat-card .info .stat-title {
            font-size: 0.75rem;
            color: #6c757d;
            line-height: 1.1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }


        .status-badge { padding: 5px 12px; border-radius: 20px; font-weight: 600; font-size: 0.8rem; }
        .status-badge.delivered { background-color: #d4edda; color: #155724; }
        .status-badge.pending { background-color: #fff3cd; color: #856404; }
        .status-badge.completed { background-color: #d1ecf1; color: #0c5460; }
        .hidden { display: none !important; }

        /* Horizontal Navigation Bar */
        .horizontal-nav {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .horizontal-nav-list {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 10px;
        }

        .horizontal-nav-item {
            display: flex;
        }

        .horizontal-nav-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            text-decoration: none;
            color: var(--text-color-light);
            font-weight: 500;
            font-size: 0.9rem;
            line-height: 1.2;
            transition: background-color 0.2s, color 0.2s;
            cursor: pointer;
            height: 44px;
            box-sizing: border-box;
            border-radius: 6px;
            border: 1px solid transparent;
        }

        .horizontal-nav-link:hover {
            background-color: var(--background-color-light);
            border-color: var(--border-color-light);
        }

        .horizontal-nav-link.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .horizontal-nav-link i {
            font-size: 1.3rem;
            margin-bottom: 6px;
        }

        /* Forms */
        .form-card {
            background-color: var(--card-bg-light);
            padding: 30px;
            border-radius: 8px;
            border: 1px solid var(--border-color-light);
        }

        /* Product Selection */
        .product-selection-section {
            margin-bottom: 2rem;
        }

        .product-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .product-item {
            border: 2px solid var(--border-color-light);
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: white;
        }

        .product-item:hover {
            border-color: var(--primary-color);
            background-color: #f8f9ff;
        }

        .product-item.selected {
            border-color: var(--primary-color);
            background-color: #f0f7ff;
        }

        .product-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .product-item h4 {
            margin: 0;
            font-size: 1rem;
            color: var(--text-color-light);
        }

        .product-item .price {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .product-item-details {
            font-size: 0.9rem;
            color: #6c757d;
            line-height: 1.4;
        }

        .product-item-details div {
            margin-bottom: 5px;
        }

        .selected-product-details {
            background-color: var(--background-color-light);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            display: none;
        }

        .selected-product-details.show {
            display: block;
        }

        .selected-product-details h4 {
            margin: 0 0 15px 0;
            color: var(--primary-color);
        }

        .product-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--text-color-light);
            margin-bottom: 5px;
        }

        .detail-value {
            font-size: 0.95rem;
            color: #6c757d;
        }
        .tab-buttons { display: flex; border-bottom: 1px solid var(--border-color-light); margin-bottom: 2rem; }
        .tab-btn { background: none; border: none; padding: 15px 20px; font-size: 1.1rem; font-weight: 600; cursor: pointer; border-bottom: 3px solid transparent; }
        .tab-btn.active { border-bottom-color: var(--primary-color); color: var(--primary-color); }
        .tab-pane { display: none; }
        .tab-pane.active { display: block; }
        .form-group { margin-bottom: 1.5rem; }
        .form-group label { display: block; font-weight: 600; margin-bottom: 8px; }
        select, textarea {
            width: 100%; padding: 12px; border: 1px solid var(--border-color-light); border-radius: 5px; box-sizing: border-box;
        }
        .submit-btn {
            background-color: var(--primary-color); color: white; border: none; padding: 15px 25px; border-radius: 5px; font-size: 1rem; font-weight: 600; cursor: pointer; width: 100%; margin-top: 10px;
        }
        .item-details-content { border: 1px solid var(--border-color-light); border-radius: 8px; padding: 15px; min-height: 50px; }
        .radio-group-label { font-weight: 600; margin-bottom: 8px; display:block; }
        .radio-group { display: flex; flex-direction: column; gap: 10px; }
        .radio-group label { display: flex; align-items: center; gap: 10px; font-weight: 500; }

        /* Exchange Product Selection */
        .exchange-product-selection {
            background-color: var(--background-color-light);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 20px;
            margin-top: 10px;
        }
        .exchange-product-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .exchange-product-item:hover {
            background-color: var(--border-color-light);
        }
        .price-difference-section {
            padding: 15px;
            border: 1px solid var(--primary-color);
            background-color: #f0f7ff;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
        }

        /* Drag and Drop File Upload */
        .file-upload-label {
            display: flex; flex-direction: column; align-items: center; justify-content: center;
            padding: 30px; border: 2px dashed var(--border-color-light); border-radius: 8px;
            cursor: pointer; text-align: center; transition: background-color .2s, border-color .2s;
        }
        .file-upload-label.highlight {
            border-color: var(--primary-color);
            background-color: #f0f7ff;
        }
        .file-upload-label i { font-size: 2rem; color: var(--primary-color); margin-bottom: 10px;}
        .file-list { margin-top: 10px; font-size: 0.9rem; }
        .file-list ul { list-style-type: none; padding: 0;}
        .file-list li { background-color: #f1f1f1; padding: 5px 10px; border-radius: 4px; margin-top: 5px; }


        /* Success Popup */
        .success-popup {
            display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.6); z-index: 1000;
            align-items: center; justify-content: center;
        }
        .confirmation-content { background-color: var(--card-bg-light); padding: 40px; border-radius: 10px; text-align: center; max-width: 500px; }
        .confirmation-icon i { font-size: 60px; color: var(--success-color); margin-bottom: 20px; }
        .confirmation-details { margin: 30px 0; text-align: left; }
        .confirmation-detail { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid var(--border-color-light); }
        .confirmation-btn { padding: 12px 20px; border-radius: 5px; border: none; cursor: pointer; font-weight: 600; }
        .confirmation-btn.secondary { background-color: var(--border-color-light); }

        /* STATUS PAGE - NEW STYLES WITH ACCORDION */
        .status-items-container { display: flex; flex-direction: column; gap: 1rem; }
        .status-item-card {
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            background-color: var(--card-bg-light);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .status-accordion-header {
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--card-bg-light);
            border-bottom: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .status-accordion-header:hover {
            background-color: var(--background-color-light);
        }

        .status-accordion-header.expanded {
            border-bottom-color: var(--border-color-light);
        }

        .status-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .status-header-left {
            display: flex;
            flex-direction: column;
        }

        .status-header-left h3 {
            margin: 0;
            font-size: 1.2rem;
            color: var(--primary-color);
        }

        .status-header-left p {
            margin: 5px 0 0;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .status-header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .accordion-toggle {
            font-size: 1.2rem;
            color: var(--primary-color);
            transition: transform 0.3s ease;
        }

        .accordion-toggle.expanded {
            transform: rotate(180deg);
        }

        .status-accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .status-accordion-content.expanded {
            max-height: 1000px;
        }

        .status-accordion-body {
            padding: 0 20px 20px 20px;
        }
        .status-card-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;}
        .status-card-header h3 { margin: 0; font-size: 1.3rem; }
        .status-card-header p { margin: 5px 0 0; color: #6c757d; }
        .status-card-product { display: flex; align-items: center; gap: 20px; border-bottom: 1px solid var(--border-color-light); padding-bottom: 20px; margin-bottom: 20px; }
        .status-product-details { display: flex; flex-wrap: wrap; gap: 10px 30px; flex-grow: 1; }
        .status-product-details > div { display: flex; flex-direction: column; }
        .status-product-details span:first-child { font-weight: 600; }
        .status-product-details span:last-child { color: #6c757d; }

        .progress-tracker { display: flex; justify-content: space-between; position: relative; margin: 30px 0; }
        .progress-tracker::before {
            content: '';
            position: absolute;
            top: 15px; /* (icon height / 2) */
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--border-color-light);
            z-index: 1;
        }
        .progress-step { display: flex; flex-direction: column; align-items: center; text-align: center; width: 120px; position: relative; z-index: 2; }
        .progress-icon {
            width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center;
            background-color: var(--card-bg-light); border: 2px solid var(--border-color-light);
            margin-bottom: 10px;
        }
        .progress-icon i { font-size: 14px; }
        .progress-step-title { font-weight: 600; font-size: 0.9rem; }
        .progress-step-date { font-size: 0.8rem; color: #6c757d; }

        .progress-step.completed .progress-icon { border-color: var(--success-color); background-color: var(--success-color); color: white; }
        .progress-step.active .progress-icon { border-color: var(--primary-color); background-color: var(--primary-color); color: white; }
        .progress-step.active .progress-step-title { color: var(--primary-color); }

        .status-actions { display: flex; gap: 15px; margin-top: 20px; }
        .status-actions .action-btn { background-color: transparent; border: 1px solid var(--border-color-light); padding: 10px 15px; border-radius: 5px; font-weight: 600; cursor: pointer; }
        .status-actions .action-btn.primary { background-color: var(--primary-color); color: white; border-color: var(--primary-color); }

        /* Sorting and Filtering Controls */
        .status-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 15px;
            background-color: var(--card-bg-light);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            gap: 20px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-label {
            font-weight: 600;
            color: var(--text-color-light);
            font-size: 0.9rem;
        }

        .control-select {
            padding: 8px 12px;
            border: 1px solid var(--border-color-light);
            border-radius: 5px;
            background-color: white;
            font-size: 0.9rem;
            cursor: pointer;
            min-width: 150px;
        }

        .filter-buttons {
            display: flex;
            gap: 8px;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 1px solid var(--border-color-light);
            background-color: white;
            border-radius: 5px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-btn:hover {
            background-color: var(--background-color-light);
        }

        .filter-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .status-items-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .status-item-card.hidden {
            display: none;
        }

        /* Policy Buttons */
        .policy-btn {
            background-color: var(--card-bg-light);
            border: 1px solid var(--border-color-light);
            color: var(--text-color-light);
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            line-height: 1.2;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            height: 44px;
            box-sizing: border-box;
        }

        .policy-btn:hover {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .policy-btn i {
            font-size: 0.9rem;
        }

        /* Modal Styles */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: 2000;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-content {
            background-color: white;
            border-radius: 10px;
            max-width: 800px;
            width: 100%;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .modal-header h2 {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6c757d;
            padding: 5px;
            border-radius: 3px;
            transition: background-color 0.2s;
        }

        .modal-close:hover {
            background-color: var(--background-color-light);
        }

        .modal-body {
            padding: 25px;
            overflow-y: auto;
            flex: 1;
            line-height: 1.6;
        }

        .modal-body h3 {
            color: var(--primary-color);
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .modal-body h3:first-child {
            margin-top: 0;
        }

        .modal-body p {
            margin-bottom: 15px;
            color: var(--text-color-light);
        }

        .modal-body ul {
            margin-bottom: 15px;
            padding-left: 20px;
        }

        .modal-body li {
            margin-bottom: 8px;
            color: var(--text-color-light);
        }

        .modal-footer {
            padding: 20px 25px;
            border-top: 1px solid var(--border-color-light);
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            flex-shrink: 0;
        }

        .download-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.2s;
        }

        .download-btn:hover {
            background-color: #0056b3;
        }

        .close-btn {
            background-color: var(--border-color-light);
            color: var(--text-color-light);
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .close-btn:hover {
            background-color: #c0c4cc;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .top-section {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .dashboard-area {
                min-width: auto;
            }

            .dashboard-container {
                justify-content: center;
                flex-wrap: wrap;
                height: auto;
                gap: 10px;
            }

            .stat-card {
                min-width: 160px;
                flex: 1;
                height: 44px;
            }

            .policy-buttons-area {
                justify-content: center;
            }

            .navigation-area {
                align-self: center;
            }
        }

        @media (min-width: 769px) {
            .top-section {
                gap: 20px;
            }
        }
    </style>

</head>
<body>
    <div class="app-container">
        <!-- Top Section: Dashboard + Navigation -->
        <div class="top-section">
            <div class="dashboard-area">
                <section id="dashboard-section" class="content-section">
                    <div class="dashboard-container">
                        <div class="stat-card">
                            <div class="icon"><i class="fas fa-shopping-cart"></i></div>
                            <div class="info">
                                <div id="orders-count" class="stat-value">15</div>
                                <div class="stat-title">Total Orders</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="icon"><i class="fas fa-undo-alt"></i></div>
                            <div class="info">
                                <div id="returned-count" class="stat-value">3</div>
                                <div class="stat-title">Items Returned</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="icon"><i class="fas fa-exchange-alt"></i></div>
                            <div class="info">
                                <div id="exchanged-count" class="stat-value">2</div>
                                <div class="stat-title">Items Exchanged</div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <div class="policy-buttons-area">
                <button class="policy-btn" onclick="openModal('policies')">
                    <i class="fas fa-file-alt"></i>
                    <span>Return & Exchange Policies</span>
                </button>
                <button class="policy-btn" onclick="openModal('terms')">
                    <i class="fas fa-gavel"></i>
                    <span>Terms & Conditions</span>
                </button>
            </div>

            <div class="navigation-area">
                <!-- Horizontal Navigation Bar -->
                <nav class="horizontal-nav">
                    <ul class="horizontal-nav-list">
                        <li class="horizontal-nav-item">
                            <a href="#" class="horizontal-nav-link active" data-section="request">
                                <i class="fas fa-plus-circle"></i>
                                <span>New Request</span>
                            </a>
                        </li>
                        <li class="horizontal-nav-item">
                            <a href="#" class="horizontal-nav-link" data-section="status">
                                <i class="fas fa-tasks"></i>
                                <span>Status</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Bottom Section: Full-width content -->
        <div class="bottom-section">
            <div class="content-area">
                <!-- Status Section -->
                <div id="status-content" class="status-section">
                    <section id="status-section" class="content-section">
                        <h1>Request Status</h1>
                        <p class="section-description">Track the progress of your submitted return and exchange requests.</p>

                        <!-- Sorting and Filtering Controls -->
                        <div class="status-controls">
                            <div class="control-group">
                                <label class="control-label" for="sortSelect">Sort by:</label>
                                <select id="sortSelect" class="control-select">
                                    <option value="date-desc">Date: Latest to Oldest</option>
                                    <option value="date-asc">Date: Oldest to Latest</option>
                                    <option value="type-returns">Type: Returns First</option>
                                    <option value="type-exchanges">Type: Exchanges First</option>
                                </select>
                            </div>

                            <div class="control-group">
                                <span class="control-label">Filter by Status:</span>
                                <div class="filter-buttons">
                                    <button class="filter-btn active" data-filter="all">All</button>
                                    <button class="filter-btn" data-filter="pending">Pending</button>
                                    <button class="filter-btn" data-filter="active">In Progress</button>
                                    <button class="filter-btn" data-filter="completed">Completed</button>
                                </div>
                            </div>
                        </div>

                        <div id="status-items-container" class="status-items-container">
                            <!-- Status items will be added here by JavaScript -->
                        </div>
                    </section>
                </div>

                <!-- New Request Section -->
                <div id="request-content" class="content-section-container active">
                    <section id="request-section" class="content-section">
                        <h1>New Return or Exchange Request</h1>
                        <p class="section-description">Create a new return or exchange request.</p>
                    <div class="form-card">
                        <div class="tab-buttons">
                            <button class="tab-btn active" data-tab="return">Return</button>
                            <button class="tab-btn" data-tab="exchange">Exchange</button>
                        </div>
                        <div class="tab-content">
                            <div id="return" class="tab-pane active">
                                <form id="returnForm">
                                    <div class="form-group product-selection-section">
                                        <label>Select Product to Return*</label>
                                        <div class="product-list" id="returnProductList">
                                            <div class="product-item" data-order="ORD12345" data-product="Bosch Spark Plug NGK-7092" data-price="12.99" onclick="selectProduct(this, 'return')">
                                                <div class="product-item-header">
                                                    <h4>Bosch Spark Plug NGK-7092</h4>
                                                    <span class="price">$12.99</span>
                                                </div>
                                                <div class="product-item-details">
                                                    <div><strong>Order:</strong> ORD12345</div>
                                                    <div><strong>Purchased:</strong> Nov 10, 2023</div>
                                                    <div><strong>Fitment:</strong> 2018-2022 Honda Civic</div>
                                                </div>
                                            </div>
                                            <div class="product-item" data-order="ORD12346" data-product="ACDelco Oil Filter PF48" data-price="8.49" onclick="selectProduct(this, 'return')">
                                                <div class="product-item-header">
                                                    <h4>ACDelco Oil Filter PF48</h4>
                                                    <span class="price">$8.49</span>
                                                </div>
                                                <div class="product-item-details">
                                                    <div><strong>Order:</strong> ORD12346</div>
                                                    <div><strong>Purchased:</strong> Nov 8, 2023</div>
                                                    <div><strong>Fitment:</strong> 2015-2020 Toyota Camry</div>
                                                </div>
                                            </div>
                                            <div class="product-item" data-order="ORD12347" data-product="Monroe Shock Absorber 72339" data-price="45.99" onclick="selectProduct(this, 'return')">
                                                <div class="product-item-header">
                                                    <h4>Monroe Shock Absorber 72339</h4>
                                                    <span class="price">$45.99</span>
                                                </div>
                                                <div class="product-item-details">
                                                    <div><strong>Order:</strong> ORD12347</div>
                                                    <div><strong>Purchased:</strong> Nov 5, 2023</div>
                                                    <div><strong>Fitment:</strong> 2016-2021 Ford F-150</div>
                                                </div>
                                            </div>
                                            <div class="product-item" data-order="ORD12348" data-product="Fram Air Filter CA10467" data-price="15.29" onclick="selectProduct(this, 'return')">
                                                <div class="product-item-header">
                                                    <h4>Fram Air Filter CA10467</h4>
                                                    <span class="price">$15.29</span>
                                                </div>
                                                <div class="product-item-details">
                                                    <div><strong>Order:</strong> ORD12348</div>
                                                    <div><strong>Purchased:</strong> Nov 3, 2023</div>
                                                    <div><strong>Fitment:</strong> 2017-2023 Nissan Altima</div>
                                                </div>
                                            </div>
                                            <div class="product-item" data-order="ORD12349" data-product="Mobil 1 Motor Oil 5W-30" data-price="24.99" onclick="selectProduct(this, 'return')">
                                                <div class="product-item-header">
                                                    <h4>Mobil 1 Motor Oil 5W-30</h4>
                                                    <span class="price">$24.99</span>
                                                </div>
                                                <div class="product-item-details">
                                                    <div><strong>Order:</strong> ORD12349</div>
                                                    <div><strong>Purchased:</strong> Nov 1, 2023</div>
                                                    <div><strong>Fitment:</strong> Universal - 5 Quart</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="returnSelectedProduct" class="selected-product-details">
                                            <h4>Selected Product Details</h4>
                                            <div class="product-details-grid">
                                                <div class="detail-item">
                                                    <span class="detail-label">Product Name</span>
                                                    <span class="detail-value" id="returnSelectedName">-</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Order Number</span>
                                                    <span class="detail-value" id="returnSelectedOrder">-</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Price</span>
                                                    <span class="detail-value" id="returnSelectedPrice">-</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Purchase Date</span>
                                                    <span class="detail-value" id="returnSelectedDate">-</span>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" id="returnSelectedProductData" required>
                                    </div>
                                <div class="form-group">
                                    <label for="returnReason">Reason for Return*</label>
                                    <select id="returnReason" required>
                                        <option value="">Select a reason...</option>
                                        <option value="wrong-part">Wrong Part Received</option>
                                        <option value="damaged">Part Damaged/Defective</option>
                                        <option value="not-as-described">Not as Described</option>
                                        <option value="no-longer-needed">No Longer Needed</option>
                                        <option value="better-price">Found Better Price Elsewhere</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="returnComments">Comments*</label>
                                    <textarea id="returnComments" rows="4" placeholder="Please provide details about your return request..." required></textarea>
                                </div>
                                 <div class="form-group">
                                    <label>Upload Image/Video Evidence*</label>
                                    <div class="file-upload-container">
                                        <label for="returnFiles" class="file-upload-label">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <span>Choose files or drag & drop</span>
                                        </label>
                                        <input type="file" id="returnFiles" class="hidden" multiple required>
                                        <div id="returnFilesList" class="file-list"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <span class="radio-group-label">Return Method*</span>
                                    <div class="radio-group">
                                        <label><input type="radio" name="returnMethod" value="courier" checked> Courier Pickup</label>
                                        <label><input type="radio" name="returnMethod" value="store"> Return to Store</label>
                                        <label><input type="radio" name="returnMethod" value="warehouse"> Warehouse Drop-off</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <span class="radio-group-label">Refund Method*</span>
                                    <div class="radio-group">
                                        <label><input type="radio" name="refundMethod" value="original" checked> Original Payment Method</label>
                                        <label><input type="radio" name="refundMethod" value="credit"> Store Credit</label>
                                    </div>
                                </div>
                                <button type="submit" class="submit-btn">Submit Return Request</button>
                            </form>
                        </div>
                            <div id="exchange" class="tab-pane">
                                <form id="exchangeForm">
                                    <div class="form-group product-selection-section">
                                        <label>Select Product to Exchange*</label>
                                        <div class="product-list" id="exchangeProductList">
                                            <div class="product-item" data-order="ORD12350" data-product="Denso Brake Pads D905" data-price="32.99" onclick="selectProduct(this, 'exchange')">
                                                <div class="product-item-header">
                                                    <h4>Denso Brake Pads D905</h4>
                                                    <span class="price">$32.99</span>
                                                </div>
                                                <div class="product-item-details">
                                                    <div><strong>Order:</strong> ORD12350</div>
                                                    <div><strong>Purchased:</strong> Oct 28, 2023</div>
                                                    <div><strong>Fitment:</strong> 2019-2023 Subaru Outback</div>
                                                </div>
                                            </div>
                                            <div class="product-item" data-order="ORD12351" data-product="Valvoline Transmission Fluid" data-price="18.75" onclick="selectProduct(this, 'exchange')">
                                                <div class="product-item-header">
                                                    <h4>Valvoline Transmission Fluid</h4>
                                                    <span class="price">$18.75</span>
                                                </div>
                                                <div class="product-item-details">
                                                    <div><strong>Order:</strong> ORD12351</div>
                                                    <div><strong>Purchased:</strong> Oct 25, 2023</div>
                                                    <div><strong>Fitment:</strong> Universal ATF+4</div>
                                                </div>
                                            </div>
                                            <div class="product-item" data-order="ORD12352" data-product="Michelin Wiper Blades 24in" data-price="21.49" onclick="selectProduct(this, 'exchange')">
                                                <div class="product-item-header">
                                                    <h4>Michelin Wiper Blades 24in</h4>
                                                    <span class="price">$21.49</span>
                                                </div>
                                                <div class="product-item-details">
                                                    <div><strong>Order:</strong> ORD12352</div>
                                                    <div><strong>Purchased:</strong> Oct 22, 2023</div>
                                                    <div><strong>Fitment:</strong> Driver Side - 24 inch</div>
                                                </div>
                                            </div>
                                            <div class="product-item" data-order="ORD12353" data-product="K&N Performance Air Filter" data-price="39.99" onclick="selectProduct(this, 'exchange')">
                                                <div class="product-item-header">
                                                    <h4>K&N Performance Air Filter</h4>
                                                    <span class="price">$39.99</span>
                                                </div>
                                                <div class="product-item-details">
                                                    <div><strong>Order:</strong> ORD12353</div>
                                                    <div><strong>Purchased:</strong> Oct 20, 2023</div>
                                                    <div><strong>Fitment:</strong> 2014-2019 Jeep Cherokee</div>
                                                </div>
                                            </div>
                                            <div class="product-item" data-order="ORD12354" data-product="Castrol GTX Motor Oil 10W-40" data-price="19.99" onclick="selectProduct(this, 'exchange')">
                                                <div class="product-item-header">
                                                    <h4>Castrol GTX Motor Oil 10W-40</h4>
                                                    <span class="price">$19.99</span>
                                                </div>
                                                <div class="product-item-details">
                                                    <div><strong>Order:</strong> ORD12354</div>
                                                    <div><strong>Purchased:</strong> Oct 18, 2023</div>
                                                    <div><strong>Fitment:</strong> Universal - 5 Quart</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="exchangeSelectedProduct" class="selected-product-details">
                                            <h4>Selected Product Details</h4>
                                            <div class="product-details-grid">
                                                <div class="detail-item">
                                                    <span class="detail-label">Product Name</span>
                                                    <span class="detail-value" id="exchangeSelectedName">-</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Order Number</span>
                                                    <span class="detail-value" id="exchangeSelectedOrder">-</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Price</span>
                                                    <span class="detail-value" id="exchangeSelectedPrice">-</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Purchase Date</span>
                                                    <span class="detail-value" id="exchangeSelectedDate">-</span>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" id="exchangeSelectedProductData" required>
                                    </div>
                                <div class="form-group">
                                    <label for="exchangeReason">Reason for Exchange*</label>
                                    <select id="exchangeReason" required>
                                        <option value="">Select a reason...</option>
                                        <option value="wrong-part">Wrong Part Received</option>
                                        <option value="incorrect-fitment">Incorrect Fitment</option>
                                        <option value="damaged">Part Damaged/Defective</option>
                                        <option value="upgrade">Upgrade/Different Part</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                 <div class="form-group">
                                    <label>Upload Image/Video Evidence*</label>
                                     <div class="file-upload-container">
                                        <label for="exchangeFiles" class="file-upload-label">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <span>Choose files or drag & drop</span>
                                        </label>
                                        <input type="file" id="exchangeFiles" class="hidden" multiple required>
                                        <div id="exchangeFilesList" class="file-list"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="exchangeComments">Comments*</label>
                                    <textarea id="exchangeComments" rows="4" placeholder="Please provide details about your exchange request..." required></textarea>
                                </div>
                                <div class="form-group">
                                    <label>Choose New Product*</label>
                                    <div class="exchange-product-selection" id="exchangeProductSelection">
                                        <!-- Mock product list -->
                                        <div class="exchange-product-item" onclick="handleExchangeSelection(this)" data-name="Premium Spark Plug" data-price="19.99">
                                            <span>Premium Spark Plug</span><strong>$19.99</strong>
                                        </div>
                                         <div class="exchange-product-item" onclick="handleExchangeSelection(this)" data-name="Performance Oil Filter" data-price="15.49">
                                            <span>Performance Oil Filter</span><strong>$15.49</strong>
                                        </div>
                                        <div class="exchange-product-item" onclick="handleExchangeSelection(this)" data-name="Standard Wiper Blade" data-price="7.99">
                                            <span>Standard Wiper Blade</span><strong>$7.99</strong>
                                        </div>
                                    </div>
                                    <div class="item-details-content hidden" id="newProductDetails"></div>
                                    <input type="hidden" id="newProductName" name="newProductName" required>
                                </div>
                                            <div id="priceDifferenceSection" class="hidden"></div>
                                            <button type="submit" class="submit-btn">Submit Exchange Request</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>

    <div id="confirmationPopup" class="success-popup">
        <div class="confirmation-content">
            <div class="confirmation-icon"><i class="fas fa-check-circle"></i></div>
            <h3>Request Submitted!</h3>
            <div class="confirmation-details">
                <div class="confirmation-detail"><span class="detail-label">Request #:</span><span class="detail-value" id="requestNumber"></span></div>
                <div class="confirmation-detail"><span class="detail-label">Order #:</span><span class="detail-value" id="confirmationOrderNumber"></span></div>
                <div class="confirmation-detail"><span class="detail-label">Type:</span><span class="detail-value" id="requestType"></span></div>
            </div>
            <div class="confirmation-actions">
                <button id="viewStatusBtn" class="confirmation-btn">View Status</button>
                <button id="closePopupBtn" class="confirmation-btn secondary">Close</button>
            </div>
        </div>
    </div>

    <!-- Policy Modal -->
    <div id="policiesModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Return & Exchange Policies</h2>
                <button class="modal-close" onclick="closeModal('policies')">&times;</button>
            </div>
            <div class="modal-body">
                <h3>Return Policy</h3>
                <p>AutoParts Express is committed to your satisfaction. We offer a comprehensive return policy to ensure you receive the right parts for your vehicle.</p>

                <h3>Return Eligibility</h3>
                <ul>
                    <li>Items must be returned within 30 days of purchase</li>
                    <li>Products must be in original, unused condition</li>
                    <li>Original packaging and documentation required</li>
                    <li>Electrical components must be unopened and uninstalled</li>
                    <li>Custom or special-order items are non-returnable</li>
                </ul>

                <h3>Return Process</h3>
                <p>To initiate a return:</p>
                <ul>
                    <li>Submit a return request through our online portal</li>
                    <li>Provide order number and reason for return</li>
                    <li>Upload photos showing product condition</li>
                    <li>Print the prepaid return shipping label</li>
                    <li>Package items securely and ship within 5 business days</li>
                </ul>

                <h3>Exchange Policy</h3>
                <p>We offer exchanges for the following reasons:</p>
                <ul>
                    <li>Wrong part received due to our error</li>
                    <li>Defective or damaged products</li>
                    <li>Incorrect fitment information provided</li>
                    <li>Upgrade to different specifications</li>
                </ul>

                <h3>Refund Processing</h3>
                <p>Refunds are processed within 3-5 business days after we receive and inspect your returned items. Refunds will be issued to the original payment method. Shipping costs are non-refundable unless the return is due to our error.</p>

                <h3>Warranty Coverage</h3>
                <p>All automotive parts come with manufacturer warranties. Warranty claims should be directed to the manufacturer. We provide assistance with warranty documentation and claims processing.</p>
            </div>
            <div class="modal-footer">
                <button class="download-btn" onclick="downloadPDF('policies')">
                    <i class="fas fa-download"></i>
                    Download Brochure
                </button>
                <button class="close-btn" onclick="closeModal('policies')">Close</button>
            </div>
        </div>
    </div>

    <!-- Terms Modal -->
    <div id="termsModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Terms & Conditions</h2>
                <button class="modal-close" onclick="closeModal('terms')">&times;</button>
            </div>
            <div class="modal-body">
                <h3>Acceptance of Terms</h3>
                <p>By using AutoParts Express services, you agree to be bound by these Terms and Conditions. These terms apply to all users of our website, mobile applications, and services.</p>

                <h3>Product Information</h3>
                <ul>
                    <li>We strive to provide accurate product descriptions and fitment information</li>
                    <li>Product images are for illustration purposes and may vary from actual items</li>
                    <li>Specifications are subject to change by manufacturers without notice</li>
                    <li>Fitment guides are recommendations - final compatibility verification is customer responsibility</li>
                </ul>

                <h3>Pricing and Payment</h3>
                <ul>
                    <li>All prices are subject to change without notice</li>
                    <li>Prices include applicable taxes where required</li>
                    <li>Payment is due at time of order placement</li>
                    <li>We accept major credit cards, PayPal, and financing options</li>
                    <li>Promotional pricing may have specific terms and limitations</li>
                </ul>

                <h3>Shipping and Delivery</h3>
                <p>Shipping times are estimates and not guaranteed. Delivery delays may occur due to weather, carrier issues, or other circumstances beyond our control. Risk of loss transfers to customer upon delivery.</p>

                <h3>Limitation of Liability</h3>
                <p>AutoParts Express shall not be liable for any indirect, incidental, special, or consequential damages arising from the use of our products or services. Our total liability shall not exceed the purchase price of the product.</p>

                <h3>Privacy Policy</h3>
                <p>We are committed to protecting your privacy. Personal information is collected and used in accordance with our Privacy Policy, which is incorporated by reference into these terms.</p>

                <h3>Dispute Resolution</h3>
                <p>Any disputes arising from these terms shall be resolved through binding arbitration in accordance with the rules of the American Arbitration Association.</p>

                <h3>Modifications</h3>
                <p>We reserve the right to modify these terms at any time. Continued use of our services constitutes acceptance of any modifications.</p>
            </div>
            <div class="modal-footer">
                <button class="download-btn" onclick="downloadPDF('terms')">
                    <i class="fas fa-download"></i>
                    Download Brochure
                </button>
                <button class="close-btn" onclick="closeModal('terms')">Close</button>
            </div>
        </div>
    </div>


    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // --- INITIALIZATION ---
        initHorizontalNav();
        initRequestForms();
        initConfirmationPopup();
        loadSampleStatusData();
        initStatusControls();
        initModals();
    });

    // --- DASHBOARD ---
    function updateDashboard(type, count) {
        if(type === 'Return') {
            const returnedEl = document.getElementById('returned-count');
            returnedEl.textContent = parseInt(returnedEl.textContent) + count;
        } else if (type === 'Exchange') {
            const exchangedEl = document.getElementById('exchanged-count');
            exchangedEl.textContent = parseInt(exchangedEl.textContent) + count;
        }
        // Initial call
        if(!type) {
            document.getElementById('orders-count').textContent = document.querySelectorAll('.order-item').length;
        }
    }

    // --- HORIZONTAL NAVIGATION ---
    function initHorizontalNav() {
        const navLinks = document.querySelectorAll('.horizontal-nav-link');

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.currentTarget.dataset.section;

                // Update active state
                navLinks.forEach(l => l.classList.remove('active'));
                e.currentTarget.classList.add('active');

                // Hide all content sections
                hideAllContentSections();

                // Show the selected section
                showContentSection(section);
            });
        });

        // Set default active section on page load
        showContentSection('request');
    }

    function hideAllContentSections() {
        const sections = [
            document.getElementById('status-content'),
            document.getElementById('request-content')
        ];

        sections.forEach(section => {
            if (section) {
                section.classList.remove('active');
            }
        });
    }

    function showContentSection(sectionName) {
        let targetSection;

        switch(sectionName) {
            case 'status':
                targetSection = document.getElementById('status-content');
                break;
            case 'request':
                targetSection = document.getElementById('request-content');
                break;
            default:
                targetSection = document.getElementById('request-content');
        }

        if (targetSection) {
            targetSection.classList.add('active');
        }
    }

    // --- NAVIGATION HELPERS ---
    function switchTab(type) {
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        document.querySelector(`.tab-btn[data-tab="${type}"]`).classList.add('active');
        document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
        document.getElementById(type).classList.add('active');
    }

    window.navigateToRequest = (type) => {
        // Show the request section
        hideAllContentSections();
        showContentSection('request');

        // Update navigation active state
        const navLinks = document.querySelectorAll('.horizontal-nav-link');
        navLinks.forEach(l => l.classList.remove('active'));
        document.querySelector('.horizontal-nav-link[data-section="request"]').classList.add('active');

        // Switch to the appropriate tab
        switchTab(type);
    };



    // --- REQUEST FORMS ---
    function initRequestForms() {
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.target.dataset.tab;
                switchTab(tabId);
                localStorage.removeItem('selectedProducts');
            });
        });

        initDragAndDrop('returnFiles', 'returnFilesList');
        initDragAndDrop('exchangeFiles', 'exchangeFilesList');

        document.getElementById('returnForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            if(!validateForm('return')) return;

            const selectedProductData = JSON.parse(document.getElementById('returnSelectedProductData').value);
            const requestNumber = 'REQ-' + Math.floor(100000 + Math.random() * 900000);
            addStatusItem(requestNumber, 'Return', selectedProductData);
            showConfirmationPopup('Return', selectedProductData.orderNumber, requestNumber);
            document.getElementById('returnForm').reset();
            document.getElementById('returnFilesList').innerHTML = '';
            document.getElementById('returnSelectedProduct').classList.remove('show');
            document.querySelectorAll('#returnProductList .product-item').forEach(item => item.classList.remove('selected'));

            // Switch to status section after successful submission
            setTimeout(() => {
                hideAllContentSections();
                showContentSection('status');
                const navLinks = document.querySelectorAll('.horizontal-nav-link');
                navLinks.forEach(l => l.classList.remove('active'));
                document.querySelector('.horizontal-nav-link[data-section="status"]').classList.add('active');
            }, 2000); // Wait 2 seconds to show confirmation popup first
        });

        document.getElementById('exchangeForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            if(!validateForm('exchange')) return;

            const selectedProductData = JSON.parse(document.getElementById('exchangeSelectedProductData').value);
            const requestNumber = 'REQ-' + Math.floor(100000 + Math.random() * 900000);
            addStatusItem(requestNumber, 'Exchange', selectedProductData);
            showConfirmationPopup('Exchange', selectedProductData.orderNumber, requestNumber);
            document.getElementById('exchangeForm').reset();
            document.getElementById('exchangeFilesList').innerHTML = '';
            document.getElementById('newProductDetails').innerHTML = '';
            document.getElementById('priceDifferenceSection').innerHTML = '';
            document.getElementById('exchangeSelectedProduct').classList.remove('show');
            document.querySelectorAll('#exchangeProductList .product-item').forEach(item => item.classList.remove('selected'));

            // Switch to status section after successful submission
            setTimeout(() => {
                hideAllContentSections();
                showContentSection('status');
                const navLinks = document.querySelectorAll('.horizontal-nav-link');
                navLinks.forEach(l => l.classList.remove('active'));
                document.querySelector('.horizontal-nav-link[data-section="status"]').classList.add('active');
            }, 2000); // Wait 2 seconds to show confirmation popup first
        });
    }

    function validateForm(type) {
        const form = document.getElementById(`${type}Form`);
        const selectedProductData = form.querySelector(`#${type}SelectedProductData`);
        const reason = form.querySelector(`#${type}Reason`);
        const files = form.querySelector(`#${type}Files`);
        const comments = form.querySelector(`#${type}Comments`);
        const newProduct = form.querySelector('#newProductName');

        if (!selectedProductData.value) {
            alert("Please select a product from the list.");
            return false;
        }
        if (reason.value === "") {
            alert("Please select a reason for the request.");
            reason.focus();
            return false;
        }
        if (!comments.value.trim()) {
            alert("Please provide comments for your request.");
            comments.focus();
            return false;
        }
        if (files.files.length === 0) {
            alert("Please upload at least one image or video.");
            files.focus();
            return false;
        }
        if (type === 'exchange' && !newProduct.value) {
             alert("Please select a product to exchange for.");
             return false;
        }
        return true;
    }

    window.handleExchangeSelection = (el) => {
        const newProductName = el.dataset.name;
        const newPrice = parseFloat(el.dataset.price);

        document.getElementById('newProductName').value = newProductName;
        const newProductDetails = document.getElementById('newProductDetails');
        newProductDetails.innerHTML = `<h4>Exchanging for: ${newProductName} ($${newPrice.toFixed(2)})</h4>`;
        newProductDetails.classList.remove('hidden');
        document.getElementById('exchangeProductSelection').classList.add('hidden');

        // For now, we'll assume a default original price of $10 since we don't have order data
        // In a real application, this would be fetched from the order system
        const originalTotal = 10.00;
        const difference = newPrice - originalTotal;
        const diffSection = document.getElementById('priceDifferenceSection');
        diffSection.classList.remove('hidden');

        if (difference > 0) {
            diffSection.innerHTML = `<div class="price-difference-section">
                <strong>Amount Due: $${difference.toFixed(2)}</strong>
                <button type="button" class="submit-btn" style="width: auto; margin-left: 20px;">Pay Difference</button>
            </div>`;
        } else if (difference < 0) {
             diffSection.innerHTML = `<div class="price-difference-section">
                <strong>Refund Due: $${Math.abs(difference).toFixed(2)}</strong>
                <div class="radio-group" style="margin-top: 10px; justify-content: center; flex-direction: row;">
                    <label><input type="radio" name="exchangeRefund" value="original" checked> Original Method</label>
                    <label><input type="radio" name="exchangeRefund" value="credit"> Store Credit</label>
                </div>
            </div>`;
        } else {
            diffSection.classList.add('hidden');
        }
    }

    function initDragAndDrop(inputId, listId) {
        const fileInput = document.getElementById(inputId);
        const dropLabel = document.querySelector(`label[for="${inputId}"]`);
        const fileListDiv = document.getElementById(listId);

        if (!fileInput || !dropLabel || !fileListDiv) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropLabel.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropLabel.addEventListener(eventName, () => dropLabel.classList.add('highlight'), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropLabel.addEventListener(eventName, () => dropLabel.classList.remove('highlight'), false);
        });

        dropLabel.addEventListener('drop', handleDrop, false);
        fileInput.addEventListener('change', (e) => handleFiles(e.target.files));

        function preventDefaults(e) { e.preventDefault(); e.stopPropagation(); }
        function handleDrop(e) { fileInput.files = e.dataTransfer.files; handleFiles(fileInput.files); }
        function handleFiles(files) {
            fileListDiv.innerHTML = "";
            if (files.length > 0) {
                const list = document.createElement("ul");
                Array.from(files).forEach(file => {
                    const listItem = document.createElement("li");
                    listItem.textContent = `${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
                    list.appendChild(listItem);
                });
                fileListDiv.appendChild(list);
            }
        }
    }

    // --- SAMPLE DATA ---
    function loadSampleStatusData() {
        const container = document.getElementById('status-items-container');

        // Sample return request
        const returnCard = createSampleStatusCard('REQ-123456', 'Return', 'Bosch Spark Plug', 'ORD12345', 'Wrong Part Received', '$12.99', 'Received wrong spark plug model for my vehicle', 'Nov 15, 2023', 'completed');

        // Sample exchange request
        const exchangeCard = createSampleStatusCard('REQ-789012', 'Exchange', 'ACDelco Oil Filter', 'ORD67890', 'Incorrect Fitment', '$8.49', 'Filter does not fit my engine model', 'Nov 18, 2023', 'pending');

        // Sample recent return
        const recentCard = createSampleStatusCard('REQ-345678', 'Return', 'Monroe Shock Absorber', 'ORD11111', 'Part Damaged/Defective', '$45.99', 'Shock absorber arrived with visible damage', 'Nov 20, 2023', 'active');

        container.appendChild(returnCard);
        container.appendChild(exchangeCard);
        container.appendChild(recentCard);
    }

    function createSampleStatusCard(requestNumber, type, productName, orderNumber, reason, amount, comments, date, status) {
        const newCard = document.createElement('div');
        newCard.className = 'status-item-card';
        newCard.setAttribute('data-type', type.toLowerCase());
        newCard.setAttribute('data-status', status);
        newCard.setAttribute('data-date', date);

        let progressTrackerHTML = '';
        if (type === 'Return') {
            if (status === 'completed') {
                progressTrackerHTML = `
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${date}</div></div>
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-thumbs-up"></i></div><div class="progress-step-title">Request Approved</div><div class="progress-step-date">Nov 16, 2023</div></div>
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Nov 17, 2023</div></div>
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Quality Check</div><div class="progress-step-date">Nov 18, 2023</div></div>
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Refund Processed</div><div class="progress-step-date">Nov 19, 2023</div></div>
                `;
            } else if (status === 'active') {
                progressTrackerHTML = `
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${date}</div></div>
                    <div class="progress-step completed"><div class="progress-icon"><i class="fas fa-thumbs-up"></i></div><div class="progress-step-title">Request Approved</div><div class="progress-step-date">Nov 21, 2023</div></div>
                    <div class="progress-step active"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">In Progress</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Quality Check</div><div class="progress-step-date">Pending</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Refund Processed</div><div class="progress-step-date">Pending</div></div>
                `;
            } else {
                progressTrackerHTML = `
                    <div class="progress-step active"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${date}</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-thumbs-up"></i></div><div class="progress-step-title">Request Approved</div><div class="progress-step-date">Pending</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Pending</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Quality Check</div><div class="progress-step-date">Pending</div></div>
                    <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Refund Processed</div><div class="progress-step-date">Pending</div></div>
                `;
            }
        } else {
            progressTrackerHTML = `
                <div class="progress-step active"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${date}</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Approved</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Completed</div><div class="progress-step-date">Pending</div></div>
            `;
        }

        const statusBadgeClass = status === 'completed' ? 'completed' : 'pending';
        const statusText = status === 'completed' ? 'Completed' : status === 'active' ? 'In Progress' : 'Pending';

        newCard.innerHTML = `
            <div class="status-accordion-header" onclick="toggleAccordion(this)">
                <div class="status-header-content">
                    <div class="status-header-left">
                        <h3>${type} Request #${requestNumber}</h3>
                        <p>Submitted on ${date} • ${productName}</p>
                    </div>
                    <div class="status-header-right">
                        <span class="status-badge ${statusBadgeClass}">${statusText}</span>
                        <i class="fas fa-chevron-down accordion-toggle"></i>
                    </div>
                </div>
            </div>
            <div class="status-accordion-content">
                <div class="status-accordion-body">
                    <div class="status-card-product">
                        <div class="status-product-details">
                            <div><span>${productName}</span><span>Product(s)</span></div>
                            <div><span>${orderNumber}</span><span>Order #</span></div>
                            <div><span>${reason}</span><span>Reason</span></div>
                            <div><span>${amount}</span><span>Amount</span></div>
                            <div><span>${comments}</span><span>Comments</span></div>
                        </div>
                    </div>
                    <div class="progress-tracker">
                        ${progressTrackerHTML}
                    </div>
                    <div class="status-actions">
                        <button class="action-btn"><i class="fas fa-headset"></i> Contact Support</button>
                        <button class="action-btn"><i class="fas fa-times"></i> Cancel ${type}</button>
                    </div>
                </div>
            </div>
        `;

        return newCard;
    }

    // --- STATUS & CONFIRMATION ---
    function initConfirmationPopup() {
        document.getElementById('viewStatusBtn')?.addEventListener('click', () => {
            document.getElementById('confirmationPopup').style.display = 'none';
            document.getElementById('status-section').scrollIntoView();
        });
        document.getElementById('closePopupBtn')?.addEventListener('click', () => {
             document.getElementById('confirmationPopup').style.display = 'none';
        });
    }

    // --- STATUS CONTROLS (SORTING & FILTERING) ---
    function initStatusControls() {
        // Initialize sort dropdown
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', handleSortChange);
        }

        // Initialize filter buttons
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.addEventListener('click', handleFilterChange);
        });
    }

    function handleSortChange(e) {
        const sortValue = e.target.value;
        sortStatusItems(sortValue);
    }

    function handleFilterChange(e) {
        const filterValue = e.target.dataset.filter;

        // Update active filter button
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        e.target.classList.add('active');

        // Apply filter
        filterStatusItems(filterValue);
    }

    function sortStatusItems(sortType) {
        const container = document.getElementById('status-items-container');
        const items = Array.from(container.children);

        items.sort((a, b) => {
            switch(sortType) {
                case 'date-desc':
                    return new Date(getItemDate(b)) - new Date(getItemDate(a));
                case 'date-asc':
                    return new Date(getItemDate(a)) - new Date(getItemDate(b));
                case 'type-returns':
                    const aType = getItemType(a);
                    const bType = getItemType(b);
                    if (aType === bType) return new Date(getItemDate(b)) - new Date(getItemDate(a));
                    return aType === 'Return' ? -1 : 1;
                case 'type-exchanges':
                    const aTypeEx = getItemType(a);
                    const bTypeEx = getItemType(b);
                    if (aTypeEx === bTypeEx) return new Date(getItemDate(b)) - new Date(getItemDate(a));
                    return aTypeEx === 'Exchange' ? -1 : 1;
                default:
                    return 0;
            }
        });

        // Re-append sorted items
        items.forEach(item => container.appendChild(item));
    }

    function filterStatusItems(filterType) {
        const items = document.querySelectorAll('.status-item-card');

        items.forEach(item => {
            const itemStatus = getItemStatus(item);
            let shouldShow = false;

            switch(filterType) {
                case 'all':
                    shouldShow = true;
                    break;
                case 'pending':
                    shouldShow = itemStatus === 'pending';
                    break;
                case 'active':
                    shouldShow = itemStatus === 'active';
                    break;
                case 'completed':
                    shouldShow = itemStatus === 'completed';
                    break;
            }

            if (shouldShow) {
                item.classList.remove('hidden');
            } else {
                item.classList.add('hidden');
            }
        });
    }

    function getItemDate(item) {
        const dateText = item.querySelector('.status-header-left p').textContent;
        const dateMatch = dateText.match(/(\w{3} \d{1,2}, \d{4})/);
        return dateMatch ? dateMatch[1] : new Date();
    }

    function getItemType(item) {
        const titleText = item.querySelector('.status-header-left h3').textContent;
        return titleText.includes('Return') ? 'Return' : 'Exchange';
    }

    function getItemStatus(item) {
        const statusBadge = item.querySelector('.status-badge');
        if (statusBadge.classList.contains('completed')) return 'completed';
        if (statusBadge.textContent.includes('In Progress')) return 'active';
        return 'pending';
    }

    // --- PRODUCT SELECTION ---
    window.selectProduct = function(productElement, formType) {
        // Remove selection from other products in the same form
        const productList = productElement.parentElement;
        productList.querySelectorAll('.product-item').forEach(item => {
            item.classList.remove('selected');
        });

        // Select the clicked product
        productElement.classList.add('selected');

        // Get product data
        const orderNumber = productElement.dataset.order;
        const productName = productElement.dataset.product;
        const price = productElement.dataset.price;
        const purchaseDate = productElement.querySelector('.product-item-details div').textContent.split('Purchased: ')[1];

        // Update selected product details
        const detailsSection = document.getElementById(`${formType}SelectedProduct`);
        document.getElementById(`${formType}SelectedName`).textContent = productName;
        document.getElementById(`${formType}SelectedOrder`).textContent = orderNumber;
        document.getElementById(`${formType}SelectedPrice`).textContent = `$${price}`;
        document.getElementById(`${formType}SelectedDate`).textContent = purchaseDate;

        // Store product data in hidden input
        const productData = {
            orderNumber,
            productName,
            price,
            purchaseDate
        };
        document.getElementById(`${formType}SelectedProductData`).value = JSON.stringify(productData);

        // Show the details section
        detailsSection.classList.add('show');
    };

    // --- MODAL FUNCTIONALITY ---
    function initModals() {
        // Close modal when clicking outside
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                document.querySelectorAll('.modal-overlay.show').forEach(modal => {
                    modal.classList.remove('show');
                });
            }
        });
    }

    window.openModal = function(type) {
        const modalId = type === 'policies' ? 'policiesModal' : 'termsModal';
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
            // Prevent body scroll when modal is open
            document.body.style.overflow = 'hidden';
        }
    };

    window.closeModal = function(type) {
        const modalId = type === 'policies' ? 'policiesModal' : 'termsModal';
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
            // Restore body scroll
            document.body.style.overflow = '';
        }
    };

    window.downloadPDF = function(type) {
        // Create a simple download simulation
        const filename = type === 'policies' ? 'Return-Exchange-Policies.pdf' : 'Terms-Conditions.pdf';
        const content = type === 'policies' ?
            'AutoParts Express - Return & Exchange Policies\n\nThis document contains our complete return and exchange policies...' :
            'AutoParts Express - Terms & Conditions\n\nThis document contains our complete terms and conditions...';

        // Create a blob with the content
        const blob = new Blob([content], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);

        // Create a temporary download link
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Show confirmation
        alert(`${filename} has been downloaded successfully!`);
    };

    // --- ACCORDION FUNCTIONALITY ---
    window.toggleAccordion = function(header) {
        const content = header.nextElementSibling;
        const toggle = header.querySelector('.accordion-toggle');
        const isExpanded = content.classList.contains('expanded');

        if (isExpanded) {
            content.classList.remove('expanded');
            header.classList.remove('expanded');
            toggle.classList.remove('expanded');
        } else {
            content.classList.add('expanded');
            header.classList.add('expanded');
            toggle.classList.add('expanded');
        }
    };

    function addStatusItem(requestNumber, type, productData) {
        const container = document.getElementById('status-items-container');
        const form = document.getElementById(`${type.toLowerCase()}Form`);

        const reasonEl = form.querySelector(`#${type.toLowerCase()}Reason`);
        const commentsEl = form.querySelector(`#${type.toLowerCase()}Comments`);

        const orderNumber = productData.orderNumber;
        const productName = productData.productName;
        const reasonText = reasonEl.options[reasonEl.selectedIndex].text;
        const commentsText = commentsEl.value.trim();
        const totalAmount = parseFloat(productData.price);
        const today = new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });

        const newCard = document.createElement('div');
        newCard.className = 'status-item-card';
        newCard.setAttribute('data-type', type.toLowerCase());
        newCard.setAttribute('data-status', 'pending');
        newCard.setAttribute('data-date', today);

        let progressTrackerHTML = '';
        if (type === 'Return') {
            progressTrackerHTML = `
                <div class="progress-step active"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${today}</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-thumbs-up"></i></div><div class="progress-step-title">Request Approved</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Quality Check</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Refund Processed</div><div class="progress-step-date">Pending</div></div>
            `;
        } else {
             progressTrackerHTML = `
                <div class="progress-step active"><div class="progress-icon"><i class="fas fa-check"></i></div><div class="progress-step-title">Request Submitted</div><div class="progress-step-date">${today}</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-box"></i></div><div class="progress-step-title">Approved</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-tasks"></i></div><div class="progress-step-title">Package Received</div><div class="progress-step-date">Pending</div></div>
                <div class="progress-step"><div class="progress-icon"><i class="fas fa-undo-alt"></i></div><div class="progress-step-title">Completed</div><div class="progress-step-date">Pending</div></div>
            `;
        }

        newCard.innerHTML = `
            <div class="status-accordion-header" onclick="toggleAccordion(this)">
                <div class="status-header-content">
                    <div class="status-header-left">
                        <h3>${type} Request #${requestNumber}</h3>
                        <p>Submitted on ${today} • ${productName}</p>
                    </div>
                    <div class="status-header-right">
                        <span class="status-badge pending">Pending</span>
                        <i class="fas fa-chevron-down accordion-toggle"></i>
                    </div>
                </div>
            </div>
            <div class="status-accordion-content">
                <div class="status-accordion-body">
                    <div class="status-card-product">
                        <div class="status-product-details">
                            <div><span>${productName}</span><span>Product(s)</span></div>
                            <div><span>${orderNumber}</span><span>Order #</span></div>
                            <div><span>${reasonText}</span><span>Reason</span></div>
                            <div><span>$${totalAmount.toFixed(2)}</span><span>Amount</span></div>
                            <div><span>${commentsText}</span><span>Comments</span></div>
                        </div>
                    </div>
                    <div class="progress-tracker">
                        ${progressTrackerHTML}
                    </div>
                    <div class="status-actions">
                        <button class="action-btn"><i class="fas fa-headset"></i> Contact Support</button>
                        <button class="action-btn"><i class="fas fa-times"></i> Cancel ${type}</button>
                    </div>
                </div>
            </div>
        `;
        container.prepend(newCard);
        updateDashboard(type, 1);

        // Apply current sort and filter to new item
        const currentSort = document.getElementById('sortSelect').value;
        const currentFilter = document.querySelector('.filter-btn.active').dataset.filter;
        sortStatusItems(currentSort);
        filterStatusItems(currentFilter);
    }

    function showConfirmationPopup(type, orderNumber, requestNumber) {
        document.getElementById('requestType').textContent = type;
        document.getElementById('confirmationOrderNumber').textContent = orderNumber;
        document.getElementById('requestNumber').textContent = requestNumber;
        document.getElementById('confirmationPopup').style.display = 'flex';
    }
    </script>
</body>
</html>
