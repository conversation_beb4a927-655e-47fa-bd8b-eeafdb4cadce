<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Form Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        #testResults { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>Returns Application Form Test Suite</h1>
    
    <div class="test-section">
        <h2>Test Controls</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="testReturnForm()">Test Return Form</button>
        <button onclick="testExchangeForm()">Test Exchange Form</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="testResults"></div>
    
    <script>
        let testResults = [];
        
        function logResult(test, status, message) {
            testResults.push({ test, status, message, timestamp: new Date() });
            updateDisplay();
        }
        
        function updateDisplay() {
            const container = document.getElementById('testResults');
            container.innerHTML = '<h2>Test Results</h2>';
            
            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.status}`;
                div.innerHTML = `
                    <strong>${result.test}</strong>: ${result.message}
                    <small style="float: right;">${result.timestamp.toLocaleTimeString()}</small>
                `;
                container.appendChild(div);
            });
        }
        
        function clearResults() {
            testResults = [];
            updateDisplay();
        }
        
        async function runAllTests() {
            clearResults();
            logResult('Test Suite', 'info', 'Starting comprehensive form tests...');
            
            // Test basic functionality
            await testBasicFunctionality();
            
            // Test return form
            await testReturnForm();
            
            // Test exchange form
            await testExchangeForm();
            
            logResult('Test Suite', 'success', 'All tests completed!');
        }
        
        async function testBasicFunctionality() {
            logResult('Basic Test', 'info', 'Testing basic application functionality...');
            
            try {
                // Create a hidden iframe to test the application
                const iframe = document.createElement('iframe');
                iframe.src = 'returns.html';
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
                
                await new Promise((resolve) => {
                    iframe.onload = resolve;
                });
                
                const iframeDoc = iframe.contentDocument;
                const iframeWindow = iframe.contentWindow;
                
                // Test if main elements exist
                const returnForm = iframeDoc.getElementById('returnForm');
                const exchangeForm = iframeDoc.getElementById('exchangeForm');
                
                if (returnForm && exchangeForm) {
                    logResult('Forms', 'success', 'Both return and exchange forms found');
                } else {
                    logResult('Forms', 'error', 'Missing forms - Return: ' + !!returnForm + ', Exchange: ' + !!exchangeForm);
                }
                
                // Test if functions exist
                const functions = ['validateForm', 'selectProduct', 'addStatusItem', 'showConfirmationPopup', 'toggleAccordion'];
                functions.forEach(funcName => {
                    if (typeof iframeWindow[funcName] === 'function') {
                        logResult('Function', 'success', `${funcName} function exists`);
                    } else {
                        logResult('Function', 'error', `${funcName} function missing`);
                    }
                });
                
                // Test product selection
                const returnProducts = iframeDoc.querySelectorAll('#returnProductList .product-item');
                const exchangeProducts = iframeDoc.querySelectorAll('#exchangeProductList .product-item');
                
                logResult('Products', 'info', `Found ${returnProducts.length} return products, ${exchangeProducts.length} exchange products`);
                
                // Clean up
                document.body.removeChild(iframe);
                
            } catch (error) {
                logResult('Basic Test', 'error', 'Error during basic testing: ' + error.message);
            }
        }
        
        async function testReturnForm() {
            logResult('Return Form', 'info', 'Testing return form submission...');
            
            try {
                const iframe = document.createElement('iframe');
                iframe.src = 'returns.html';
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
                
                await new Promise((resolve) => {
                    iframe.onload = resolve;
                });
                
                const iframeDoc = iframe.contentDocument;
                const iframeWindow = iframe.contentWindow;
                
                // Test product selection
                const firstProduct = iframeDoc.querySelector('#returnProductList .product-item');
                if (firstProduct) {
                    // Simulate product selection
                    iframeWindow.selectProduct(firstProduct, 'return');
                    
                    const selectedData = iframeDoc.getElementById('returnSelectedProductData');
                    if (selectedData && selectedData.value) {
                        logResult('Return Form', 'success', 'Product selection works');
                    } else {
                        logResult('Return Form', 'error', 'Product selection failed');
                    }
                }
                
                // Test form validation
                const returnForm = iframeDoc.getElementById('returnForm');
                if (returnForm) {
                    // Fill form with test data
                    const reason = iframeDoc.getElementById('returnReason');
                    const comments = iframeDoc.getElementById('returnComments');
                    
                    if (reason) reason.value = 'wrong-part';
                    if (comments) comments.value = 'Test comment for return';
                    
                    // Test validation without files
                    const isValid = iframeWindow.validateForm('return');
                    if (!isValid) {
                        logResult('Return Form', 'success', 'Form validation correctly requires files');
                    } else {
                        logResult('Return Form', 'error', 'Form validation should require files');
                    }
                }
                
                document.body.removeChild(iframe);
                
            } catch (error) {
                logResult('Return Form', 'error', 'Error testing return form: ' + error.message);
            }
        }
        
        async function testExchangeForm() {
            logResult('Exchange Form', 'info', 'Testing exchange form submission...');
            
            try {
                const iframe = document.createElement('iframe');
                iframe.src = 'returns.html';
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
                
                await new Promise((resolve) => {
                    iframe.onload = resolve;
                });
                
                const iframeDoc = iframe.contentDocument;
                const iframeWindow = iframe.contentWindow;
                
                // Switch to exchange tab
                const exchangeTab = iframeDoc.querySelector('.tab-btn[data-tab="exchange"]');
                if (exchangeTab) {
                    exchangeTab.click();
                    logResult('Exchange Form', 'success', 'Successfully switched to exchange tab');
                }
                
                // Test product selection
                const firstProduct = iframeDoc.querySelector('#exchangeProductList .product-item');
                if (firstProduct) {
                    iframeWindow.selectProduct(firstProduct, 'exchange');
                    
                    const selectedData = iframeDoc.getElementById('exchangeSelectedProductData');
                    if (selectedData && selectedData.value) {
                        logResult('Exchange Form', 'success', 'Exchange product selection works');
                    } else {
                        logResult('Exchange Form', 'error', 'Exchange product selection failed');
                    }
                }
                
                // Test new product selection
                const newProductItem = iframeDoc.querySelector('.exchange-product-item');
                if (newProductItem) {
                    iframeWindow.handleExchangeSelection(newProductItem);
                    
                    const newProductName = iframeDoc.getElementById('newProductName');
                    if (newProductName && newProductName.value) {
                        logResult('Exchange Form', 'success', 'New product selection works');
                    } else {
                        logResult('Exchange Form', 'error', 'New product selection failed');
                    }
                }
                
                document.body.removeChild(iframe);
                
            } catch (error) {
                logResult('Exchange Form', 'error', 'Error testing exchange form: ' + error.message);
            }
        }
        
        // Auto-run basic test on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                logResult('Auto Test', 'info', 'Running automatic basic functionality test...');
                testBasicFunctionality();
            }, 1000);
        });
    </script>
</body>
</html>
