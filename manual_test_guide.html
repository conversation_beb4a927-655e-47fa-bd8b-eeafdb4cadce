<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Test Guide</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .step { margin: 15px 0; padding: 15px; border-left: 4px solid #007bff; background-color: #f8f9fa; }
        .step h3 { margin-top: 0; color: #007bff; }
        .expected { background-color: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .code { background-color: #f1f1f1; padding: 5px; border-radius: 3px; font-family: monospace; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Manual Test Guide for Returns Application</h1>
    
    <p>Follow these steps to manually test the form submission functionality:</p>
    
    <div class="step">
        <h3>Step 1: Load the Application</h3>
        <p>Open the returns.html file in your browser. The application should load with the "New Request" section active.</p>
        <div class="expected">
            <strong>Expected:</strong> Application loads successfully, showing the return form by default.
        </div>
    </div>
    
    <div class="step">
        <h3>Step 2: Test Return Form Submission</h3>
        <ol>
            <li>Click on a product from the "Select Product to Return" list</li>
            <li>Select a reason from the "Reason for Return" dropdown</li>
            <li>Enter comments in the "Comments" field</li>
            <li>Upload at least one file (you can select any image file)</li>
            <li>Click "Submit Return Request"</li>
        </ol>
        <div class="expected">
            <strong>Expected:</strong> 
            <ul>
                <li>Product details appear when selected</li>
                <li>Form validates all required fields</li>
                <li>Confirmation popup appears with request number</li>
                <li>After 2 seconds, automatically switches to Status section</li>
                <li>New status item appears at the top of the status list</li>
            </ul>
        </div>
    </div>
    
    <div class="step">
        <h3>Step 3: Test Exchange Form Submission</h3>
        <ol>
            <li>Click the "Exchange" tab</li>
            <li>Click on a product from the "Select Product to Exchange" list</li>
            <li>Select a reason from the "Reason for Exchange" dropdown</li>
            <li>Upload at least one file</li>
            <li>Enter comments in the "Comments" field</li>
            <li>Click on a new product from "Choose New Product" section</li>
            <li>Click "Submit Exchange Request"</li>
        </ol>
        <div class="expected">
            <strong>Expected:</strong> Same behavior as return form, plus price difference calculation should appear.
        </div>
    </div>
    
    <div class="step">
        <h3>Step 4: Test Form Validation</h3>
        <p>Try submitting forms without filling required fields:</p>
        <ul>
            <li>Without selecting a product</li>
            <li>Without selecting a reason</li>
            <li>Without entering comments</li>
            <li>Without uploading files</li>
            <li>For exchange: without selecting a new product</li>
        </ul>
        <div class="expected">
            <strong>Expected:</strong> Alert messages should appear for each missing field.
        </div>
    </div>
    
    <div class="step">
        <h3>Step 5: Test Status Section</h3>
        <ol>
            <li>Navigate to the Status section</li>
            <li>Click on accordion headers to expand/collapse status items</li>
            <li>Test sorting and filtering controls</li>
        </ol>
        <div class="expected">
            <strong>Expected:</strong> Status items should expand/collapse smoothly, and sorting/filtering should work.
        </div>
    </div>
    
    <div class="step">
        <h3>Step 6: Test Report Side Panel</h3>
        <ol>
            <li>Click "Returns & Exchanges Report" in navigation</li>
            <li>Verify the side panel opens from the right</li>
            <li>Check that the chart displays correctly</li>
            <li>Close the panel using the X button or ESC key</li>
        </ol>
        <div class="expected">
            <strong>Expected:</strong> Side panel should slide in smoothly with chart and statistics.
        </div>
    </div>
    
    <div class="warning">
        <strong>Common Issues to Check:</strong>
        <ul>
            <li>If forms don't submit, check browser console for JavaScript errors</li>
            <li>If validation doesn't work, ensure all form fields have correct IDs</li>
            <li>If status items don't appear, check if the status container exists</li>
            <li>If confirmation popup doesn't show, verify the popup HTML structure</li>
        </ul>
    </div>
    
    <div class="step">
        <h3>Embedded Application Test</h3>
        <p>Test the application directly below:</p>
        <iframe src="returns.html"></iframe>
    </div>
    
    <div class="step">
        <h3>Debugging Information</h3>
        <p>If issues occur, check the browser console (F12) for error messages. Common fixes:</p>
        <ul>
            <li><span class="code">validateForm is not defined</span> - Function scope issue</li>
            <li><span class="code">Cannot read property 'value' of null</span> - Missing form element</li>
            <li><span class="code">addStatusItem is not defined</span> - Function not accessible</li>
        </ul>
    </div>
    
    <script>
        // Add some debugging helpers
        window.addEventListener('message', function(event) {
            if (event.data.type === 'test-result') {
                console.log('Test result:', event.data);
            }
        });
        
        // Monitor iframe for errors
        window.addEventListener('load', function() {
            const iframe = document.querySelector('iframe');
            if (iframe) {
                iframe.addEventListener('load', function() {
                    console.log('Application loaded in iframe');
                    try {
                        const iframeWindow = iframe.contentWindow;
                        const iframeDoc = iframe.contentDocument;
                        
                        // Check if main functions exist
                        const functions = ['validateForm', 'selectProduct', 'addStatusItem', 'showConfirmationPopup'];
                        functions.forEach(func => {
                            if (typeof iframeWindow[func] === 'function') {
                                console.log(`✓ ${func} function exists`);
                            } else {
                                console.error(`✗ ${func} function missing`);
                            }
                        });
                        
                        // Check if main elements exist
                        const elements = ['returnForm', 'exchangeForm', 'status-items-container', 'confirmationPopup'];
                        elements.forEach(id => {
                            if (iframeDoc.getElementById(id)) {
                                console.log(`✓ Element ${id} exists`);
                            } else {
                                console.error(`✗ Element ${id} missing`);
                            }
                        });
                        
                    } catch (e) {
                        console.log('Cannot access iframe content (normal for file:// URLs)');
                    }
                });
            }
        });
    </script>
</body>
</html>
